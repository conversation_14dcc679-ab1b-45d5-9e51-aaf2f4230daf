import * as vscode from "vscode";
import { GitHubApiService, type IGitHubApiService } from "./api/github";
import { registerCommands } from "./commands";
import { ContextManager, type IContextManager } from "./services/contextManager";
import { DownloadService, type IDownloadService } from "./services/download"; // Import IDownloadService
import {
    ExplorerStateService,
    type IExplorerStateService,
} from "./services/explorerStateService";
import { HttpClient, type IHttpClient } from "./services/httpClient";
import { type ILogger, Logger } from "./services/logger";
import { type IPreviewService, PreviewService } from "./services/previewService";
import {
    type IRateLimitManager,
    RateLimitManager,
} from "./services/rateLimitManager";
import { type ISelectionService, SelectionService } from "./services/selection";
import {
    type IStatusBarService,
    StatusBarService,
} from "./services/statusBarService";
import { type IStorageService, StorageService } from "./services/storage";
import {
    type IUpdateCheckService,
    UpdateCheckService,
} from "./services/updateCheckService";
import {
    type IXenditKnowledgeService,
    XenditKnowledgeService,
} from "./services/xenditKnowledgeService";
import { registerChatTools } from "./tools/tools";
import { registerXenditCopilotChatParticipant } from "./tools/xenditCopilotParticipant";
import { ExplorerView } from "./views/explorer/explorerView";
// Removed import for UpdatesTreeProvider
import { WelcomeView } from "./views/welcome/welcomeView";

// Module-level variable to store storage service for cleanup on deactivation
let globalStorageService: IStorageService | null = null;
let globalLogger: ILogger | null = null;

export function activate(context: vscode.ExtensionContext): void {
  const logger: ILogger = new Logger("Xendit Copilot", true);
  logger.info("Xendit Copilot extension is now active");

  const storageService: IStorageService = new StorageService(context);

  // Store references for cleanup on deactivation
  globalStorageService = storageService;
  globalLogger = logger;

  const config = vscode.workspace.getConfiguration("xendit");
  const showWelcomeOnStartup =
    config.get<boolean>("showWelcomeOnStartup") ?? true;
  const autoRefreshInterval = config.get<number | null>(
    "autoRefreshInterval",
    null
  );

  const httpClient: IHttpClient = new HttpClient(logger);
  const rateLimitManager: IRateLimitManager = new RateLimitManager(logger);
  const explorerStateService: IExplorerStateService = new ExplorerStateService(
    logger
  );

  const githubService: IGitHubApiService = new GitHubApiService(
    httpClient,
    rateLimitManager,
    logger
  );

  // Instantiate DownloadService with context
  const downloadService: IDownloadService = new DownloadService(
    logger,
    httpClient,
    githubService,
    context // Pass context
  );

  const selectionService: ISelectionService = new SelectionService(
    logger,
    explorerStateService
  );

  // Instantiate PreviewService
  const previewService: IPreviewService = new PreviewService(
    githubService,
    logger
  );

  // Instantiate UpdateCheckService
  const updateCheckService: IUpdateCheckService = new UpdateCheckService(
    logger,
    githubService,
    context
  );

  // Instantiate StatusBarService
  const statusBarService: IStatusBarService = new StatusBarService(logger);
  context.subscriptions.push(statusBarService);

  // Instantiate ContextManager for xendit-copilot functionality
  const contextManager: IContextManager = new ContextManager(
    logger,
    githubService,
    storageService
  );

  // Instantiate XenditKnowledgeService
  const xenditKnowledgeService: IXenditKnowledgeService = new XenditKnowledgeService(
    githubService,
    logger
  );

  // Removed instantiation of UpdatesTreeProvider

  const explorerView = new ExplorerView(
    context,
    githubService,
    logger,
    storageService,
    downloadService,
    selectionService,
    explorerStateService,
    updateCheckService,
    statusBarService,
    xenditKnowledgeService,
    previewService
  ); // Added xenditKnowledgeService and previewService

  // Store welcome view reference for refreshing when storage is cleared
  let welcomeView: WelcomeView | null = null;

  context.subscriptions.push(
    vscode.window.registerWebviewViewProvider(WelcomeView.VIEW_ID, {
      resolveWebviewView(webviewView) {
        welcomeView = new WelcomeView(webviewView, storageService, logger);
      },
    })
  );

  registerCommands({
    context,
    explorerView,
    githubService,
    logger,
    storageService,
    getWelcomeView: () => welcomeView,
    // updatesTreeProvider is no longer needed here
  });

  // Register xendit-copilot chat tools
  registerChatTools(context, logger, contextManager, selectionService, githubService, storageService);

  // Register xendit-copilot chat participant
  registerXenditCopilotChatParticipant(context, logger, contextManager, selectionService);

  // Initial setup: Link repository changes from ExplorerView to UpdatesTreeProvider
  // This requires ExplorerView to expose an event or have a method called by registerCommands
  // Let's modify registerCommands to handle this linkage.

  if (showWelcomeOnStartup) {
    vscode.commands.executeCommand("xendit.welcomeView.focus");
  }

  setupAutoRefresh(context, autoRefreshInterval, logger);

  // Listen for configuration changes to refresh the view if includePaths changes
  context.subscriptions.push(
    vscode.workspace.onDidChangeConfiguration((event) => {
      if (event.affectsConfiguration("xendit.includePaths")) {
        logger.info(
          "Configuration 'xendit.includePaths' changed. Refreshing explorer view."
        );
        // Refresh the main explorer view to apply the new filter
        explorerView.refreshView();
        // Optionally, refresh the updates view too, although filtering isn't applied there
        // updatesTreeProvider.refresh();
      }
    })
  );

}

function setupAutoRefresh(
  context: vscode.ExtensionContext,
  autoRefreshInterval: number | null,
  logger: ILogger,
): void {
  if (typeof autoRefreshInterval !== "number" || autoRefreshInterval < 10) {
    return;
  }

  const intervalMs = autoRefreshInterval * 1000;
  const interval = setInterval(() => {
    logger.debug(
      `Auto-refreshing repository (interval: ${autoRefreshInterval}s)`,
    );

    vscode.commands.executeCommand("xendit.refresh");
  }, intervalMs);

  context.subscriptions.push({
    dispose: () => clearInterval(interval),
  });
}

export function deactivate(): void {
  // Clear storage when extension is being uninstalled or disabled
  // This ensures clean removal and follows user preference for clean uninstallation
  if (globalStorageService && globalLogger) {
    try {
      globalLogger.info("Extension deactivating - clearing storage for clean removal");
      globalStorageService.clearStorage();
      globalLogger.info("Storage cleared successfully during deactivation");
    } catch (error) {
      // Log error but don't throw to avoid interfering with deactivation
      globalLogger.error("Error clearing storage during deactivation", error);
    }
  }

  // Clean up global references
  globalStorageService = null;
  globalLogger = null;
}
