/**
 * Type definitions for the xendit-copilot rule system
 */

/**
 * Category system for organizing rules
 */
export enum RuleCategory {
  ARCHITECTURE = 0,
  WORKFLOWS = 1,
  OPENAPI = 2,
  DOMAIN_RULES = 3,
}

/**
 * Category metadata with display information
 */
export interface ICategoryInfo {
  number: number;
  name: string;
  icon: string;
  purpose: string;
  examples: string[];
}

/**
 * Category information mapping
 */
export const CATEGORY_INFO: Record<RuleCategory, ICategoryInfo> = {
  [RuleCategory.ARCHITECTURE]: {
    number: 0,
    name: "Architecture",
    icon: "🏛️",
    purpose: "System design patterns",
    examples: ["Clean Architecture", "Onion", "3-tier"],
  },
  [RuleCategory.WORKFLOWS]: {
    number: 1,
    name: "Workflows",
    icon: "🔄",
    purpose: "Development processes",
    examples: ["PR reviews", "deployment", "CI/CD"],
  },
  [RuleCategory.OPENAPI]: {
    number: 2,
    name: "<PERSON>API",
    icon: "🌐",
    purpose: "API specifications",
    examples: ["REST", "GraphQL", "gRPC guidelines"],
  },
  [RuleCategory.DOMAIN_RULES]: {
    number: 3,
    name: "Domain Rules",
    icon: "🎯",
    purpose: "Team/project-specific",
    examples: ["Custom business rules"],
  },
};

/**
 * Parsed rule file metadata
 */
export interface IRuleMetadata {
  /** Original filename */
  filename: string;
  /** Rule category (00-03) */
  category: RuleCategory;
  /** Rule name extracted from filename */
  name: string;
  /** Optional semantic version (e.g., "1.2.0") */
  version?: string;
  /** Optional scope qualifier (e.g., "frontend", "backend") */
  specificity?: string;
  /** Full file path in repository */
  path: string;
  /** Service/folder this rule belongs to */
  service: string;
}

/**
 * Rule content with metadata
 */
export interface IRuleContent extends IRuleMetadata {
  /** Parsed markdown content */
  content: string;
  /** Content summary for search */
  summary?: string;
  /** Last modified timestamp */
  lastModified?: Date;
}

/**
 * Service mapping from main.mdc
 */
export interface IServiceMapping {
  /** Service name */
  name: string;
  /** Service description */
  description?: string;
  /** Available categories in this service */
  categories: RuleCategory[];
  /** Number of rules in each category */
  ruleCounts: Record<RuleCategory, number>;
}

/**
 * Repository structure parsed from main.mdc
 */
export interface IRepositoryStructure {
  /** Repository description */
  description?: string;
  /** Available services */
  services: Record<string, IServiceMapping>;
  /** Total rule count */
  totalRules: number;
  /** Last parsed timestamp */
  lastParsed: Date;
  /** Repository URL */
  repositoryUrl?: string;
}

/**
 * Context retrieval parameters
 */
export interface IContextRetrievalParams {
  /** User query for context retrieval */
  query: string;
  /** Maximum number of rules to retrieve */
  maxRules?: number;
  /** Specific service to search in */
  service?: string;
  /** Specific category to search in */
  category?: RuleCategory;
  /** Include rule content in response */
  includeContent?: boolean;
}

/**
 * Context retrieval result
 */
export interface IContextRetrievalResult {
  /** Retrieved rules */
  rules: IRuleContent[];
  /** Total rules found (before limiting) */
  totalFound: number;
  /** Search query used */
  query: string;
  /** Relevance scores for each rule */
  scores: number[];
  /** Repository structure used */
  repositoryStructure: IRepositoryStructure;
}

/**
 * Rule search criteria
 */
export interface IRuleSearchCriteria {
  /** Keywords to search for */
  keywords: string[];
  /** Category filter */
  category?: RuleCategory;
  /** Service filter */
  service?: string;
  /** Version filter */
  version?: string;
  /** Specificity filter */
  specificity?: string;
  /** Content search enabled */
  searchContent?: boolean;
}

/**
 * Context session data
 */
export interface IContextSession {
  /** Session ID */
  id: string;
  /** Retrieved rules in this session */
  retrievedRules: Set<string>;
  /** Repository structure cache */
  repositoryStructure?: IRepositoryStructure;
  /** Session start time */
  startTime: Date;
  /** Last activity time */
  lastActivity: Date;
}

/**
 * Error types for rule system
 */
export enum RuleSystemError {
  REPOSITORY_NOT_FOUND = "REPOSITORY_NOT_FOUND",
  MAIN_MDC_NOT_FOUND = "MAIN_MDC_NOT_FOUND",
  MAIN_MDC_PARSE_ERROR = "MAIN_MDC_PARSE_ERROR",
  RULE_FILE_NOT_FOUND = "RULE_FILE_NOT_FOUND",
  RULE_FILE_PARSE_ERROR = "RULE_FILE_PARSE_ERROR",
  INVALID_RULE_FILENAME = "INVALID_RULE_FILENAME",
  NETWORK_ERROR = "NETWORK_ERROR",
  PERMISSION_DENIED = "PERMISSION_DENIED",
}

/**
 * Rule system error with context
 */
export interface IRuleSystemError extends Error {
  type: RuleSystemError;
  context?: Record<string, unknown>;
  originalError?: Error;
}
