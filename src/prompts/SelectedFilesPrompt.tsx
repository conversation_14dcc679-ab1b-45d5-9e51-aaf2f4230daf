import { PromptElement, UserMessage } from '@vscode/prompt-tsx';
import type { PromptSizing } from '@vscode/prompt-tsx';
import { BaseInstructions } from './BaseInstructions';
import { FileContext } from './FileContext';
import type { ISelectedFilesPromptProps, IFilePromptState } from './types';

/**
 * SelectedFilesPrompt is the main orchestrating component that combines
 * base instructions, user query, and file context with proper prioritization
 */
export class SelectedFilesPrompt extends PromptElement<ISelectedFilesPromptProps, IFilePromptState> {

  async prepare(): Promise<IFilePromptState> {
    // Process files and handle any preparation logic
    const processedFiles = this.props.files.map(file => ({
      ...file,
      // Ensure language is set
      language: file.language || this.getLanguageFromPath(file.path)
    }));

    return {
      processedFiles,
      errors: []
    };
  }

  render(state: IFilePromptState, sizing: PromptSizing) {
    const {
      repository,
      userQuery,
      includeContent = true,
      maxContentLength = 10000,
      additionalInstructions
    } = this.props;

    return (
      <>
        {/* Base instructions - highest priority */}
        <BaseInstructions
          repository={repository}
          additionalInstructions={additionalInstructions}
          priority={100}
        />

        {/* User query - high priority */}
        {userQuery && (
          <UserMessage priority={90}>
            {`**User Request:**\n${userQuery}`}
          </UserMessage>
        )}

        {/* Repository context - medium priority */}
        <UserMessage priority={80}>
          {`**Repository Context:**\nRepository: **${repository.owner}/${repository.name}** (branch: ${repository.branch})\nSelected files: ${state.processedFiles.length}`}
        </UserMessage>

        {/* File contents - lower priority but with flexGrow for token management */}
        <FileContext
          files={state.processedFiles}
          includeContent={includeContent}
          maxContentLength={maxContentLength}
          priority={70}
          flexGrow={1}
        />

        {/* Error reporting if any */}
        {state.errors.length > 0 && (
          <UserMessage priority={50}>
            {`**Errors encountered:**\n${state.errors.map(error => `- ${error}`).join('\n')}`}
          </UserMessage>
        )}
      </>
    );
  }

  private getLanguageFromPath(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase() || '';

    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'ts': 'typescript',
      'jsx': 'javascript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'kt': 'kotlin',
      'swift': 'swift',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'toml': 'toml',
      'ini': 'ini',
      'cfg': 'ini',
      'conf': 'ini',
      'sh': 'bash',
      'bash': 'bash',
      'zsh': 'bash',
      'fish': 'bash',
      'ps1': 'powershell',
      'sql': 'sql',
      'md': 'markdown',
      'txt': 'text',
      'log': 'text'
    };

    return languageMap[extension] || 'text';
  }
}
