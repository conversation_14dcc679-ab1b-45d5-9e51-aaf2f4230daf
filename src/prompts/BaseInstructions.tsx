import { PromptElement, UserMessage } from '@vscode/prompt-tsx';
import type { IBaseInstructionsProps } from './types';

/**
 * BaseInstructions component provides high-priority base instructions for the language model
 * These instructions have the highest priority to ensure they're always included
 */
export class BaseInstructions extends PromptElement<IBaseInstructionsProps> {
  render() {
    const { repository, additionalInstructions } = this.props;

    let content = "You are an AI assistant helping with code analysis and development tasks.\n\n";

    if (repository) {
      content += "**Current Repository Context:**\n";
      content += `- Repository: **${repository.owner}/${repository.name}**\n`;
      content += `- Branch: **${repository.branch}**\n\n`;
    }

    content += "**Your capabilities:**\n";
    content += "- Analyze code structure and patterns\n";
    content += "- Provide implementation suggestions\n";
    content += "- Help with debugging and troubleshooting\n";
    content += "- Explain code functionality and best practices\n";
    content += "- Assist with documentation and code reviews\n\n";

    content += "**Guidelines:**\n";
    content += "- Always consider the context of the provided files\n";
    content += "- Provide specific, actionable advice\n";
    content += "- Reference file paths and line numbers when relevant\n";
    content += "- Suggest improvements while respecting existing patterns\n";
    content += "- Ask clarifying questions if the request is ambiguous\n\n";

    if (additionalInstructions) {
      content += "**Additional Instructions:**\n";
      content += additionalInstructions + "\n\n";
    }

    return <UserMessage priority={100}>{content}</UserMessage>;
  }
}
