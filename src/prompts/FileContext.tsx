import { PromptElement, UserMessage, TextChunk } from '@vscode/prompt-tsx';
import type { PromptSizing } from '@vscode/prompt-tsx';
import type { IFileContextProps, IFileData } from './types';

/**
 * FileContext component renders selected file contents with intelligent token budget management
 * Uses flexGrow to cooperatively use available token budget
 */
export class FileContext extends PromptElement<IFileContextProps> {
  render(_state: void, sizing: PromptSizing) {
    const { files, includeContent = true, maxContentLength = 10000 } = this.props;

    if (files.length === 0) {
      return <UserMessage priority={70}>No files are currently selected in the explorer view.</UserMessage>;
    }

    if (!includeContent) {
      // Just list file paths
      let content = `**Selected Files (${files.length}):**\n\n`;
      for (const file of files) {
        content += `- \`${file.path}\`\n`;
      }
      return <UserMessage priority={70}>{content}</UserMessage>;
    }

    // Render files with content using token budget management
    let content = `**Selected Files (${files.length}):**\n\n`;

    for (const file of files) {
      content += `### \`${file.path}\`\n\n`;

      if (file.error) {
        content += `❌ **Error:** ${file.error}\n\n`;
      } else if (file.content.trim()) {
        // Use TextChunk for large content that needs to fit within budget
        const formattedContent = this.formatFileContent(file, maxContentLength);
        content += formattedContent + "\n\n";
      } else {
        content += "*File is empty*\n\n";
      }
    }

    return <UserMessage priority={70} flexGrow={1}>{content}</UserMessage>;
  }

  private formatFileContent(file: IFileData, maxContentLength: number): string {
    let content = file.content;

    // Truncate content if too long
    if (content.length > maxContentLength) {
      content = content.substring(0, maxContentLength) + "\n\n... (content truncated)";
    }

    // Format with syntax highlighting
    const language = file.language || this.getLanguageFromPath(file.path);
    return `\`\`\`${language}\n${content}\n\`\`\``;
  }

  private getLanguageFromPath(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase() || '';

    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'ts': 'typescript',
      'jsx': 'javascript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'kt': 'kotlin',
      'swift': 'swift',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'toml': 'toml',
      'ini': 'ini',
      'cfg': 'ini',
      'conf': 'ini',
      'sh': 'bash',
      'bash': 'bash',
      'zsh': 'bash',
      'fish': 'bash',
      'ps1': 'powershell',
      'sql': 'sql',
      'md': 'markdown',
      'txt': 'text',
      'log': 'text'
    };

    return languageMap[extension] || 'text';
  }
}
