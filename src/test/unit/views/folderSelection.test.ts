import * as assert from "assert";
import * as sinon from "sinon";
import { ExplorerView } from "../../../views/explorer/explorerView";
import type { ILogger } from "../../../services/logger";
import type { IGitHubApiService } from "../../../api/github";
import type { IExplorerStateService } from "../../../services/explorerStateService";
import type { ISelectionService } from "../../../services/selection";
import type { ExplorerTreeItem } from "../../../views/explorer/treeItem";

suite("Folder Selection", () => {
  let explorerView: ExplorerView;
  let mockLogger: sinon.SinonStubbedInstance<ILogger>;
  let mockGithubService: sinon.SinonStubbedInstance<IGitHubApiService>;
  let mockStateService: sinon.SinonStubbedInstance<IExplorerStateService>;
  let mockSelectionService: sinon.SinonStubbedInstance<ISelectionService>;

  setup(() => {
    mockLogger = {
      debug: sinon.stub(),
      info: sinon.stub(),
      warn: sinon.stub(),
      error: sinon.stub(),
      show: sinon.stub(),
    } as any;

    mockGithubService = {
      fetchRepositoryContentRecursive: sinon.stub(),
      fetchRepositoryContent: sinon.stub(),
      fetchFileContent: sinon.stub(),
    } as any;

    mockStateService = {
      getAllItems: sinon.stub(),
    } as any;

    mockSelectionService = {
      getFolderSelectionInfo: sinon.stub(),
      toggleRecursiveSelection: sinon.stub(),
      selectItems: sinon.stub(),
      deselectItems: sinon.stub(),
    } as any;

    explorerView = new ExplorerView(
      {} as any, // context
      mockGithubService,
      mockLogger,
      {} as any, // storageService
      {} as any, // downloadService
      mockSelectionService,
      mockStateService,
      {} as any, // updateCheckService
      {} as any, // statusBarService
      {} as any, // xenditKnowledgeService
      {} as any, // previewService
    );
  });

  teardown(() => {
    sinon.restore();
  });

  test("handleSelectFolderCommand should toggle folder selection", () => {
    // Arrange
    const mockItem: ExplorerTreeItem = {
      content: {
        type: "dir",
        path: "test/folder",
        name: "folder",
        sha: "",
        size: 0,
        url: "",
        html_url: "",
        git_url: "",
        download_url: null,
      },
    } as any;

    mockSelectionService.getFolderSelectionInfo.onFirstCall().returns({ totalItems: 5, selectedItems: 0 });
    mockSelectionService.getFolderSelectionInfo.onSecondCall().returns({ totalItems: 5, selectedItems: 5 });

    // Act
    explorerView.handleSelectFolderCommand(mockItem);

    // Assert
    assert.strictEqual(mockSelectionService.toggleRecursiveSelection.calledOnce, true);
    assert.strictEqual(mockSelectionService.toggleRecursiveSelection.calledWith("test/folder"), true);
  });

  test("handleSelectAllInFolderCommand should select all items in folder", () => {
    // Arrange
    const mockItem: ExplorerTreeItem = {
      content: {
        type: "dir",
        path: "test/folder",
        name: "folder",
        sha: "",
        size: 0,
        url: "",
        html_url: "",
        git_url: "",
        download_url: null,
      },
    } as any;

    const mockItemsMap = new Map([
      ["test/folder/file1.txt", { content: { path: "test/folder/file1.txt" } } as any],
      ["test/folder/file2.txt", { content: { path: "test/folder/file2.txt" } } as any],
      ["other/file.txt", { content: { path: "other/file.txt" } } as any],
    ]);

    mockStateService.getAllItems.returns(mockItemsMap);

    // Act
    explorerView.handleSelectAllInFolderCommand(mockItem);

    // Assert
    assert.strictEqual(mockSelectionService.selectItems.calledOnce, true);
    const calledWith = mockSelectionService.selectItems.getCall(0).args[0];
    assert.strictEqual(calledWith.includes("test/folder"), true);
    assert.strictEqual(calledWith.includes("test/folder/file1.txt"), true);
    assert.strictEqual(calledWith.includes("test/folder/file2.txt"), true);
    assert.strictEqual(calledWith.includes("other/file.txt"), false);
  });

  test("handleDeselectAllInFolderCommand should deselect all items in folder", () => {
    // Arrange
    const mockItem: ExplorerTreeItem = {
      content: {
        type: "dir",
        path: "test/folder",
        name: "folder",
        sha: "",
        size: 0,
        url: "",
        html_url: "",
        git_url: "",
        download_url: null,
      },
    } as any;

    const mockItemsMap = new Map([
      ["test/folder/file1.txt", { content: { path: "test/folder/file1.txt" } } as any],
      ["test/folder/file2.txt", { content: { path: "test/folder/file2.txt" } } as any],
      ["other/file.txt", { content: { path: "other/file.txt" } } as any],
    ]);

    mockStateService.getAllItems.returns(mockItemsMap);

    // Act
    explorerView.handleDeselectAllInFolderCommand(mockItem);

    // Assert
    assert.strictEqual(mockSelectionService.deselectItems.calledOnce, true);
    const calledWith = mockSelectionService.deselectItems.getCall(0).args[0];
    assert.strictEqual(calledWith.includes("test/folder"), true);
    assert.strictEqual(calledWith.includes("test/folder/file1.txt"), true);
    assert.strictEqual(calledWith.includes("test/folder/file2.txt"), true);
    assert.strictEqual(calledWith.includes("other/file.txt"), false);
  });
});
