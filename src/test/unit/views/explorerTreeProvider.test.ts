import * as assert from "assert";
import * as sinon from "sinon";
import { ExplorerTreeProvider } from "../../../views/explorer/treeProvider";
import type { ILogger } from "../../../services/logger";
import type { IGitHubApiService } from "../../../api/github";
import type { IExplorerStateService } from "../../../services/explorerStateService";
import type { ISelectionService } from "../../../services/selection";
import type { IUpdateCheckService } from "../../../services/updateCheckService";
import type { IStatusBarService } from "../../../services/statusBarService";
import type { IXenditKnowledgeService } from "../../../services/xenditKnowledgeService";
import type { GithubRepository } from "../../../api/types";

suite("ExplorerTreeProvider", () => {
  let treeProvider: ExplorerTreeProvider;
  let mockLogger: sinon.SinonStubbedInstance<ILogger>;
  let mockGithubService: sinon.SinonStubbedInstance<IGitHubApiService>;
  let mockStateService: sinon.SinonStubbedInstance<IExplorerStateService>;
  let mockSelectionService: sinon.SinonStubbedInstance<ISelectionService>;
  let mockUpdateCheckService: sinon.SinonStubbedInstance<IUpdateCheckService>;
  let mockStatusBarService: sinon.SinonStubbedInstance<IStatusBarService>;
  let mockXenditKnowledgeService: sinon.SinonStubbedInstance<IXenditKnowledgeService>;
  let mockContext: any;

  setup(() => {
    mockLogger = {
      debug: sinon.stub(),
      info: sinon.stub(),
      warn: sinon.stub(),
      error: sinon.stub(),
      show: sinon.stub(),
    } as any;

    mockGithubService = {
      fetchRepositoryContentRecursive: sinon.stub(),
      fetchRepositoryContent: sinon.stub(),
      fetchFileContent: sinon.stub(),
    } as any;

    mockStateService = {
      getRepository: sinon.stub(),
      setRepository: sinon.stub(),
      resetState: sinon.stub(),
      getRootItems: sinon.stub(),
      setRootItems: sinon.stub(),
      isRootLoading: sinon.stub(),
      setRootLoading: sinon.stub(),
      getItem: sinon.stub(),
      getAllItems: sinon.stub(),
      mapItem: sinon.stub(),
      clearItemMap: sinon.stub(),
      getLoadingPromise: sinon.stub(),
      setLoadingPromise: sinon.stub(),
      deleteLoadingPromise: sinon.stub(),
      clearLoadingPromises: sinon.stub(),
    } as any;

    mockSelectionService = {
      isSelected: sinon.stub(),
      toggleSelection: sinon.stub(),
      clearSelection: sinon.stub(),
      getSelectedItems: sinon.stub(),
      toggleRecursiveSelection: sinon.stub(),
      selectItems: sinon.stub(),
      onDidChangeSelection: sinon.stub(),
    } as any;

    mockUpdateCheckService = {
      checkUpdates: sinon.stub(),
    } as any;

    mockStatusBarService = {
      setIdle: sinon.stub(),
      setCheckingUpdates: sinon.stub(),
      setUpdatesChecked: sinon.stub(),
    } as any;

    mockXenditKnowledgeService = {
      analyzeRepository: sinon.stub(),
    } as any;

    mockContext = {
      extensionPath: "/test/path",
    };

    treeProvider = new ExplorerTreeProvider(
      mockGithubService,
      mockLogger,
      mockSelectionService,
      mockStateService,
      mockUpdateCheckService,
      mockStatusBarService,
      mockXenditKnowledgeService,
      mockContext,
      "/test/extension/path"
    );
  });

  teardown(() => {
    sinon.restore();
  });

  test("refresh should re-analyze repository structure when repository exists", async () => {
    // Arrange
    const mockRepository: GithubRepository = {
      owner: "test-owner",
      name: "test-repo",
      branch: "main",
    };

    mockStateService.getRepository.returns(mockRepository);
    mockXenditKnowledgeService.analyzeRepository.resolves({
      categories: [],
      isXenditKnowledge: false,
    });

    // Spy on setRepository method
    const setRepositorySpy = sinon.spy(treeProvider, "setRepository");

    // Act
    treeProvider.refresh();

    // Wait a bit for async operations
    await new Promise(resolve => setTimeout(resolve, 10));

    // Assert
    assert.strictEqual(mockStateService.resetState.calledOnce, true, "resetState should be called");
    assert.strictEqual(setRepositorySpy.calledOnce, true, "setRepository should be called to re-analyze");
    assert.strictEqual(setRepositorySpy.calledWith(mockRepository), true, "setRepository should be called with current repository");
  });

  test("refresh should handle case when no repository exists", () => {
    // Arrange
    mockStateService.getRepository.returns(null);

    // Act
    treeProvider.refresh();

    // Assert
    assert.strictEqual(mockStateService.resetState.calledOnce, true, "resetState should be called");
    // setRepository should not be called when no repository exists
  });

  test("refresh should handle individual item refresh", () => {
    // Arrange
    const mockItem = {
      content: {
        type: "dir",
        path: "test/path",
        name: "test",
        sha: "",
        size: 0,
        url: "",
        html_url: "",
        git_url: "",
        download_url: null,
      },
    } as any;

    // Act
    treeProvider.refresh(mockItem);

    // Assert
    assert.strictEqual(mockStateService.deleteLoadingPromise.calledOnce, true, "deleteLoadingPromise should be called for directory");
    assert.strictEqual(mockStateService.deleteLoadingPromise.calledWith("test/path"), true, "deleteLoadingPromise should be called with correct path");
  });
});
