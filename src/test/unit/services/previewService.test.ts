import * as assert from 'assert';
import * as vscode from 'vscode';
import { PreviewService } from '../../../services/previewService';
import type { IGitHubApiService } from '../../../api/github';
import type { ILogger } from '../../../services/logger';
import type { GithubRepository } from '../../../api/types';

// Mock implementations
class MockLogger implements ILogger {
  log(message: string): void {}
  debug(message: string, ...args: any[]): void {}
  info(message: string, ...args: any[]): void {}
  warn(message: string, ...args: any[]): void {}
  error(message: string, ...args: any[]): void {}
  show(): void {}
}

class MockGitHubApiService implements IGitHubApiService {
  async fetchRepositoryContent(repository: GithubRepository, path?: string) {
    // Mock successful file response
    return {
      success: true as const,
      data: [{
        type: 'file' as const,
        path: path || '',
        size: 100,
        download_url: `https://raw.githubusercontent.com/${repository.owner}/${repository.name}/${repository.branch}/${path}`,
        sha: 'mock-sha',
        name: (path || '').split('/').pop() || '',
        url: `https://api.github.com/repos/${repository.owner}/${repository.name}/contents/${path}`,
        html_url: `https://github.com/${repository.owner}/${repository.name}/blob/${repository.branch}/${path}`,
        git_url: `https://api.github.com/repos/${repository.owner}/${repository.name}/git/blobs/mock-sha`
      }]
    };
  }

  async fetchFileContent(downloadUrl: string) {
    // Mock file content based on URL
    if (downloadUrl.includes('test.ts')) {
      return {
        success: true as const,
        data: 'console.log("Hello World");'
      };
    }
    if (downloadUrl.includes('README.md')) {
      return {
        success: true as const,
        data: '# Test Repository\n\nThis is a test.'
      };
    }
    return {
      success: true as const,
      data: 'Mock file content'
    };
  }

  async fetchRepositoryContentRecursive() {
    return { success: false as const, data: [], error: new Error('Not implemented') };
  }

  // Other required methods (not used in preview tests)
  async fetchRepositories() { return { success: false as const, data: [], error: new Error('Not implemented') }; }
  async fetchBranches() { return { success: false as const, data: [], error: new Error('Not implemented') }; }
  async searchRepositories() { return { success: false as const, data: [], error: new Error('Not implemented') }; }
  async fetchCommits() { return { success: false as const, data: [], error: new Error('Not implemented') }; }
  async fetchPullRequests() { return { success: false as const, data: [], error: new Error('Not implemented') }; }
  async fetchIssues() { return { success: false as const, data: [], error: new Error('Not implemented') }; }
  async fetchReleases() { return { success: false as const, data: [], error: new Error('Not implemented') }; }
  async fetchContributors() { return { success: false as const, data: [], error: new Error('Not implemented') }; }
  async fetchRepositoryInfo() { return { success: false as const, data: null, error: new Error('Not implemented') }; }
}

suite('PreviewService Tests', () => {
  let previewService: PreviewService;
  let mockLogger: MockLogger;
  let mockGitHubService: MockGitHubApiService;

  const mockRepository: GithubRepository = {
    owner: 'test-owner',
    name: 'test-repo',
    branch: 'main'
  };

  setup(() => {
    mockLogger = new MockLogger();
    mockGitHubService = new MockGitHubApiService();
    previewService = new PreviewService(mockGitHubService, mockLogger);
  });

  test('should identify supported file types', () => {
    // Test supported extensions
    assert.strictEqual(previewService.isSupportedFileType('test.ts'), true);
    assert.strictEqual(previewService.isSupportedFileType('test.js'), true);
    assert.strictEqual(previewService.isSupportedFileType('README.md'), true);
    assert.strictEqual(previewService.isSupportedFileType('rules.mdc'), true);
    assert.strictEqual(previewService.isSupportedFileType('config.json'), true);
    assert.strictEqual(previewService.isSupportedFileType('config.yml'), true);
    assert.strictEqual(previewService.isSupportedFileType('config.yaml'), true);
    assert.strictEqual(previewService.isSupportedFileType('style.css'), true);
    assert.strictEqual(previewService.isSupportedFileType('script.py'), true);

    // Test unsupported extensions
    assert.strictEqual(previewService.isSupportedFileType('image.png'), false);
    assert.strictEqual(previewService.isSupportedFileType('video.mp4'), false);
    assert.strictEqual(previewService.isSupportedFileType('archive.zip'), false);

    // Test special files
    assert.strictEqual(previewService.isSupportedFileType('Dockerfile'), true);
    assert.strictEqual(previewService.isSupportedFileType('Makefile'), true);
  });

  test('should get file content successfully', async () => {
    const fileData = await previewService.getFileContent('test.ts', mockRepository);

    assert.strictEqual(fileData.path, 'test.ts');
    assert.strictEqual(fileData.content, 'console.log("Hello World");');
    assert.strictEqual(fileData.language, 'typescript');
    assert.strictEqual(fileData.error, undefined);
  });

  test('should handle markdown files correctly', async () => {
    const fileData = await previewService.getFileContent('README.md', mockRepository);

    assert.strictEqual(fileData.path, 'README.md');
    assert.strictEqual(fileData.content, '# Test Repository\n\nThis is a test.');
    assert.strictEqual(fileData.language, 'markdown');
    assert.strictEqual(fileData.error, undefined);
  });

  test('should detect language from file extension', async () => {
    const testCases = [
      { file: 'test.js', expectedLang: 'javascript' },
      { file: 'test.ts', expectedLang: 'typescript' },
      { file: 'test.py', expectedLang: 'python' },
      { file: 'test.java', expectedLang: 'java' },
      { file: 'test.cpp', expectedLang: 'cpp' },
      { file: 'test.html', expectedLang: 'html' },
      { file: 'test.css', expectedLang: 'css' },
      { file: 'test.json', expectedLang: 'json' },
      { file: 'test.yaml', expectedLang: 'yaml' },
      { file: 'test.yml', expectedLang: 'yaml' },
      { file: 'rules.mdc', expectedLang: 'markdown' },
      { file: 'test.sql', expectedLang: 'sql' },
      { file: 'test.sh', expectedLang: 'bash' },
      { file: 'test.unknown', expectedLang: 'plaintext' }
    ];

    for (const testCase of testCases) {
      const fileData = await previewService.getFileContent(testCase.file, mockRepository);
      assert.strictEqual(fileData.language, testCase.expectedLang,
        `Expected ${testCase.file} to have language ${testCase.expectedLang}, got ${fileData.language}`);
    }
  });

  test('should handle empty file paths array', async () => {
    // This test would require mocking vscode.window.showInformationMessage
    // For now, we'll just verify the method exists and can be called
    try {
      await previewService.previewFiles([], mockRepository);
      // If we get here without error, the method handled empty array correctly
      assert.ok(true, 'previewFiles handled empty array without throwing');
    } catch (error) {
      // If it throws, it should be a controlled error, not a crash
      assert.ok(error instanceof Error, 'Error should be an Error instance');
    }
  });

  test('should filter unsupported file types', () => {
    const supportedFiles = [
      'src/index.ts',
      'README.md',
      'package.json',
      'style.css'
    ];

    const unsupportedFiles = [
      'image.png',
      'video.mp4',
      'archive.zip'
    ];

    supportedFiles.forEach(file => {
      assert.strictEqual(previewService.isSupportedFileType(file), true,
        `${file} should be supported`);
    });

    unsupportedFiles.forEach(file => {
      assert.strictEqual(previewService.isSupportedFileType(file), false,
        `${file} should not be supported`);
    });
  });
});
