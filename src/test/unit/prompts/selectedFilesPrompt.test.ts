import * as assert from 'assert';
import { renderElementJSON } from '@vscode/prompt-tsx';
import { SelectedFilesPrompt } from '../../../prompts/SelectedFilesPrompt';
import type { IFileData } from '../../../prompts/types';
import type { GithubRepository } from '../../../api/types';

suite('SelectedFilesPrompt Tests', () => {
  const mockRepository: GithubRepository = {
    owner: 'test-owner',
    name: 'test-repo',
    branch: 'main'
  };

  const mockFiles: IFileData[] = [
    {
      path: 'src/test.ts',
      content: 'console.log("Hello World");',
      language: 'typescript'
    },
    {
      path: 'README.md',
      content: '# Test Repository\n\nThis is a test.',
      language: 'markdown'
    }
  ];

  test('should render prompt with files', async () => {
    try {
      const promptElement = await renderElementJSON(
        SelectedFilesPrompt,
        {
          repository: mockRepository,
          files: mockFiles,
          includeContent: true,
          maxContentLength: 1000
        },
        {
          tokenBudget: 4000,
          countTokens: async (text: string) => Math.ceil(text.length / 4) // Simple approximation
        }
      );

      assert.ok(promptElement, 'Prompt element should be created');
      assert.ok(typeof promptElement === 'object', 'Prompt element should be an object');

      console.log('✅ SelectedFilesPrompt rendered successfully');
    } catch (error) {
      console.error('❌ Error rendering SelectedFilesPrompt:', error);
      throw error;
    }
  });

  test('should handle empty files array', async () => {
    try {
      const promptElement = await renderElementJSON(
        SelectedFilesPrompt,
        {
          repository: mockRepository,
          files: [],
          includeContent: true,
          maxContentLength: 1000
        },
        {
          tokenBudget: 4000,
          countTokens: async (text: string) => Math.ceil(text.length / 4)
        }
      );

      assert.ok(promptElement, 'Prompt element should be created even with empty files');
      console.log('✅ SelectedFilesPrompt handled empty files correctly');
    } catch (error) {
      console.error('❌ Error with empty files:', error);
      throw error;
    }
  });

  test('should handle files with errors', async () => {
    const filesWithErrors: IFileData[] = [
      {
        path: 'missing.ts',
        content: '',
        error: 'File not found',
        language: 'typescript'
      }
    ];

    try {
      const promptElement = await renderElementJSON(
        SelectedFilesPrompt,
        {
          repository: mockRepository,
          files: filesWithErrors,
          includeContent: true,
          maxContentLength: 1000
        },
        {
          tokenBudget: 4000,
          countTokens: async (text: string) => Math.ceil(text.length / 4)
        }
      );

      assert.ok(promptElement, 'Prompt element should handle files with errors');
      console.log('✅ SelectedFilesPrompt handled error files correctly');
    } catch (error) {
      console.error('❌ Error with error files:', error);
      throw error;
    }
  });

  test('should work with paths only mode', async () => {
    try {
      const promptElement = await renderElementJSON(
        SelectedFilesPrompt,
        {
          repository: mockRepository,
          files: mockFiles,
          includeContent: false,
          maxContentLength: 1000
        },
        {
          tokenBudget: 4000,
          countTokens: async (text: string) => Math.ceil(text.length / 4)
        }
      );

      assert.ok(promptElement, 'Prompt element should work in paths-only mode');
      console.log('✅ SelectedFilesPrompt worked in paths-only mode');
    } catch (error) {
      console.error('❌ Error in paths-only mode:', error);
      throw error;
    }
  });
});
