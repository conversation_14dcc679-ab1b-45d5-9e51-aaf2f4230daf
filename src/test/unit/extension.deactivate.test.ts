import * as assert from "assert";
import * as sinon from "sinon";
import * as vscode from "vscode";
import { activate, deactivate } from "../../extension";
import { StorageService } from "../../services/storage";

suite("Extension Deactivation Tests", () => {
  let mockContext: vscode.ExtensionContext;
  let sandbox: sinon.SinonSandbox;

  setup(() => {
    sandbox = sinon.createSandbox();
    
    // Mock VS Code extension context
    mockContext = {
      subscriptions: [],
      globalState: {
        get: sandbox.stub(),
        update: sandbox.stub(),
        keys: sandbox.stub().returns([]),
      },
      workspaceState: {
        get: sandbox.stub(),
        update: sandbox.stub(),
        keys: sandbox.stub().returns([]),
      },
      extensionUri: vscode.Uri.file("/test/path"),
      extensionPath: "/test/path",
      asAbsolutePath: sandbox.stub(),
      storageUri: vscode.Uri.file("/test/storage"),
      globalStorageUri: vscode.Uri.file("/test/global-storage"),
      logUri: vscode.Uri.file("/test/log"),
      extensionMode: vscode.ExtensionMode.Test,
      secrets: {} as any,
      environmentVariableCollection: {} as any,
      extension: {} as any,
      languageModelAccessInformation: {} as any,
    } as vscode.ExtensionContext;

    // Mock VS Code APIs
    sandbox.stub(vscode.workspace, "getConfiguration").returns({
      get: sandbox.stub().returns(true),
    } as any);

    sandbox.stub(vscode.commands, "executeCommand").resolves();
    sandbox.stub(vscode.window, "registerWebviewViewProvider").returns({
      dispose: sandbox.stub(),
    } as any);
  });

  teardown(() => {
    sandbox.restore();
  });

  test("deactivate should clear storage when called after activation", async () => {
    // Arrange
    const clearStorageSpy = sandbox.spy(StorageService.prototype, "clearStorage");
    
    // Act - activate the extension first
    activate(mockContext);
    
    // Act - deactivate the extension
    deactivate();

    // Assert
    assert.strictEqual(clearStorageSpy.calledOnce, true, "clearStorage should be called during deactivation");
  });

  test("deactivate should handle case when called before activation", () => {
    // Act - call deactivate without activation
    assert.doesNotThrow(() => {
      deactivate();
    }, "deactivate should not throw when called before activation");
  });

  test("deactivate should handle storage service errors gracefully", async () => {
    // Arrange
    const clearStorageStub = sandbox.stub(StorageService.prototype, "clearStorage");
    clearStorageStub.throws(new Error("Storage error"));
    
    // Act - activate then deactivate
    activate(mockContext);
    
    // Should not throw even if clearStorage fails
    assert.doesNotThrow(() => {
      deactivate();
    }, "deactivate should not throw even if clearStorage fails");

    // Assert
    assert.strictEqual(clearStorageStub.calledOnce, true, "clearStorage should still be called");
  });
});
