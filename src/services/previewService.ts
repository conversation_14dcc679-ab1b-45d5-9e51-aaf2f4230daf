import * as vscode from "vscode";
import type { IGitHubApiService } from "../api/github";
import type { GithubRepository } from "../api/types";
import type { ILogger } from "./logger";

/**
 * Interface for file preview data
 */
export interface IPreviewFile {
  path: string;
  content: string;
  language?: string;
  size?: number;
  error?: string;
}

/**
 * Interface for the preview service
 */
export interface IPreviewService {
  /**
   * Preview selected files in read-only editor tabs
   */
  previewFiles(filePaths: string[], repository: GithubRepository): Promise<void>;

  /**
   * Get file content for preview
   */
  getFileContent(filePath: string, repository: GithubRepository): Promise<IPreviewFile>;

  /**
   * Preview a single file in read-only editor tab
   */
  previewSingleFile(filePath: string, repository: GithubRepository): Promise<void>;

  /**
   * Check if a file type is supported for preview
   */
  isSupportedFileType(filePath: string): boolean;
}

/**
 * Service for handling file preview functionality
 */
export class PreviewService implements IPreviewService {
  private static readonly MAX_FILE_SIZE = 1024 * 1024; // 1MB limit
  private static readonly MAX_PREVIEW_FILES = 10; // Maximum files to preview at once

  // Supported file extensions for preview
  private static readonly SUPPORTED_EXTENSIONS = new Set([
    // Text files
    'txt', 'md', 'markdown', 'mdc', 'rst', 'log',
    // Code files
    'js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'kt', 'swift',
    // Web files
    'html', 'htm', 'css', 'scss', 'sass', 'less',
    // Config files
    'json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'cfg', 'conf',
    // Shell scripts
    'sh', 'bash', 'zsh', 'fish', 'ps1', 'bat', 'cmd',
    // SQL
    'sql',
    // Other
    'dockerfile', 'gitignore', 'gitattributes', 'editorconfig'
  ]);

  constructor(
    private readonly githubService: IGitHubApiService,
    private readonly logger: ILogger
  ) {}

  /**
   * Preview selected files in read-only editor tabs
   */
  async previewFiles(filePaths: string[], repository: GithubRepository): Promise<void> {
    if (filePaths.length === 0) {
      vscode.window.showInformationMessage(
        "No files selected for preview. Use the checkbox to select files."
      );
      return;
    }

    if (filePaths.length > PreviewService.MAX_PREVIEW_FILES) {
      const proceed = await vscode.window.showWarningMessage(
        `You have selected ${filePaths.length} files. Only the first ${PreviewService.MAX_PREVIEW_FILES} will be previewed. Continue?`,
        "Yes", "No"
      );
      if (proceed !== "Yes") {
        return;
      }
      filePaths = filePaths.slice(0, PreviewService.MAX_PREVIEW_FILES);
    }

    this.logger.info(`Previewing ${filePaths.length} files from ${repository.owner}/${repository.name}`);

    // Show progress for multiple files
    if (filePaths.length > 1) {
      await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "Opening file previews...",
        cancellable: true
      }, async (progress, token) => {
        const increment = 100 / filePaths.length;

        for (let i = 0; i < filePaths.length; i++) {
          if (token.isCancellationRequested) {
            break;
          }

          const filePath = filePaths[i];
          progress.report({
            increment,
            message: `Opening ${filePath}...`
          });

          await this.previewSingleFileInternal(filePath, repository);
        }
      });
    } else {
      // Single file - no progress indicator needed
      await this.previewSingleFileInternal(filePaths[0], repository);
    }
  }

  /**
   * Preview a single file in read-only editor tab
   */
  async previewSingleFile(filePath: string, repository: GithubRepository): Promise<void> {
    try {
      await this.previewSingleFileInternal(filePath, repository);
    } catch (error) {
      this.logger.error(`Error previewing file ${filePath}:`, error);
      vscode.window.showErrorMessage(
        `Failed to preview ${filePath}: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Internal preview method for a single file
   */
  private async previewSingleFileInternal(filePath: string, repository: GithubRepository): Promise<void> {
    // Check if file type is supported
    if (!this.isSupportedFileType(filePath)) {
      vscode.window.showWarningMessage(
        `File type not supported for preview: ${filePath}`
      );
      return;
    }

    // Get file content
    const fileData = await this.getFileContent(filePath, repository);

    if (fileData.error) {
      vscode.window.showErrorMessage(
        `Error loading file ${filePath}: ${fileData.error}`
      );
      return;
    }

    // Create a virtual document
    const uri = vscode.Uri.parse(`xendit-preview:${repository.owner}/${repository.name}/${filePath}`);

    // Register a text document content provider if not already registered
    this.registerContentProvider();

    // Store the content for the content provider
    this.storeFileContent(uri.toString(), fileData);

    // Open the document in preview mode
    const document = await vscode.workspace.openTextDocument(uri);
    await vscode.window.showTextDocument(document, {
      preview: true,
      preserveFocus: false,
      viewColumn: vscode.ViewColumn.Active
    });

    this.logger.debug(`Successfully opened preview for: ${filePath}`);
  }

  /**
   * Get file content for preview
   */
  async getFileContent(filePath: string, repository: GithubRepository): Promise<IPreviewFile> {
    try {
      this.logger.debug(`Fetching content for preview: ${filePath}`);

      // Get file content from GitHub
      const contentResult = await this.githubService.fetchRepositoryContent(repository, filePath);

      if (!contentResult.success || contentResult.data.length === 0) {
        return {
          path: filePath,
          content: "",
          error: "File not found or empty"
        };
      }

      const file = contentResult.data[0];
      if (file.type !== "file" || !file.download_url) {
        return {
          path: filePath,
          content: "",
          error: "Not a file or no download URL available"
        };
      }

      // Check file size
      if (file.size && file.size > PreviewService.MAX_FILE_SIZE) {
        return {
          path: filePath,
          content: "",
          error: `File too large for preview (${Math.round(file.size / 1024)}KB > ${PreviewService.MAX_FILE_SIZE / 1024}KB)`
        };
      }

      const fileContentResult = await this.githubService.fetchFileContent(file.download_url);
      if (!fileContentResult.success) {
        return {
          path: filePath,
          content: "",
          error: `Failed to fetch content: ${fileContentResult.error?.message || "Unknown error"}`
        };
      }

      return {
        path: filePath,
        content: fileContentResult.data,
        language: this.getLanguageFromPath(filePath),
        size: file.size
      };

    } catch (error) {
      this.logger.error(`Error fetching content for ${filePath}:`, error);
      return {
        path: filePath,
        content: "",
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Check if a file type is supported for preview
   */
  isSupportedFileType(filePath: string): boolean {
    const extension = filePath.split('.').pop()?.toLowerCase() || '';
    const fileName = filePath.split('/').pop()?.toLowerCase() || '';

    // Check by extension
    if (PreviewService.SUPPORTED_EXTENSIONS.has(extension)) {
      return true;
    }

    // Check special files without extensions
    const specialFiles = ['dockerfile', 'makefile', 'rakefile', 'gemfile', 'procfile'];
    if (specialFiles.includes(fileName)) {
      return true;
    }

    return false;
  }

  /**
   * Get language identifier from file path for syntax highlighting
   */
  private getLanguageFromPath(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase() || '';
    const fileName = filePath.split('/').pop()?.toLowerCase() || '';

    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'ts': 'typescript',
      'jsx': 'javascript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'kt': 'kotlin',
      'swift': 'swift',
      'html': 'html',
      'htm': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'toml': 'toml',
      'ini': 'ini',
      'cfg': 'ini',
      'conf': 'ini',
      'sh': 'bash',
      'bash': 'bash',
      'zsh': 'bash',
      'fish': 'bash',
      'ps1': 'powershell',
      'bat': 'batch',
      'cmd': 'batch',
      'sql': 'sql',
      'md': 'markdown',
      'markdown': 'markdown',
      'mdc': 'markdown',
      'rst': 'restructuredtext',
      'txt': 'plaintext',
      'log': 'log'
    };

    // Check special files
    if (fileName === 'dockerfile') return 'dockerfile';
    if (fileName === 'makefile') return 'makefile';
    if (fileName.includes('gitignore')) return 'gitignore';

    return languageMap[extension] || 'plaintext';
  }

  // Content provider for virtual documents
  private contentProvider: vscode.TextDocumentContentProvider | null = null;
  private fileContentCache = new Map<string, IPreviewFile>();

  /**
   * Register the content provider for virtual documents
   */
  private registerContentProvider(): void {
    if (this.contentProvider) {
      return; // Already registered
    }

    this.contentProvider = {
      provideTextDocumentContent: (uri: vscode.Uri): string => {
        const fileData = this.fileContentCache.get(uri.toString());
        if (!fileData) {
          return "// File content not available";
        }

        if (fileData.error) {
          return `// Error loading file: ${fileData.error}`;
        }

        return fileData.content;
      }
    };

    vscode.workspace.registerTextDocumentContentProvider('xendit-preview', this.contentProvider);
  }

  /**
   * Store file content for the content provider
   */
  private storeFileContent(uri: string, fileData: IPreviewFile): void {
    this.fileContentCache.set(uri, fileData);

    // Clean up old entries to prevent memory leaks
    if (this.fileContentCache.size > 50) {
      const entries = Array.from(this.fileContentCache.entries());
      const toDelete = entries.slice(0, 10); // Remove oldest 10 entries
      toDelete.forEach(([key]) => this.fileContentCache.delete(key));
    }
  }
}
