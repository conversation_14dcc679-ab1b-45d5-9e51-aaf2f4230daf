import type { IGitHubApiService } from "../api/github";
import type { GithubContent, GithubRepository } from "../api/types";
import type { ILogger } from "./logger";

export interface CategoryInfo {
  name: string;
  path: string;
  description?: string;
}

export interface XenditKnowledgeStructure {
  mainMdcContent?: string;
  categories: CategoryInfo[];
  isXenditKnowledge: boolean;
}

export interface IXenditKnowledgeService {
  /**
   * Check if repository is xendit-knowledge and parse its structure
   */
  analyzeRepository(repository: GithubRepository): Promise<XenditKnowledgeStructure>;

  /**
   * Parse main.mdc content to extract category information
   */
  parseMainMdc(content: string): CategoryInfo[];
}

export class XenditKnowledgeService implements IXenditKnowledgeService {
  constructor(
    private readonly githubService: IGitHubApiService,
    private readonly logger: ILogger,
  ) {}

  public async analyzeRepository(repository: GithubRepository): Promise<XenditKnowledgeStructure> {
    const isXenditKnowledge = repository.name.toLowerCase() === "xendit-knowledge";

    if (!isXenditKnowledge) {
      return {
        categories: [],
        isXenditKnowledge: false,
      };
    }

    this.logger.info(`Analyzing xendit-knowledge repository: ${repository.owner}/${repository.name}`);

    try {
      // Try to fetch main.mdc from root
      const mainMdcResult = await this.fetchMainMdc(repository);
      let categories: CategoryInfo[] = [];
      let mainMdcContent: string | undefined;

      if (mainMdcResult.success && mainMdcResult.content) {
        mainMdcContent = mainMdcResult.content;
        categories = this.parseMainMdc(mainMdcContent);
        this.logger.info(`Parsed ${categories.length} categories from main.mdc`);
      } else {
        this.logger.warn("Could not fetch main.mdc, falling back to directory discovery");
        // Fallback: discover category directories
        categories = await this.discoverCategoryDirectories(repository);
      }

      return {
        mainMdcContent,
        categories,
        isXenditKnowledge: true,
      };
    } catch (error) {
      this.logger.error("Error analyzing xendit-knowledge repository", error);
      return {
        categories: [],
        isXenditKnowledge: true,
      };
    }
  }

  public parseMainMdc(content: string): CategoryInfo[] {
    const categories: CategoryInfo[] = [];

    try {
      // Look for category patterns in main.mdc
      // Expected format: ## 00-Architecture, ## 01-Workflows, etc.
      const categoryRegex = /^##\s+(\d{2})-([^#\n]+)/gm;
      let match;

      while ((match = categoryRegex.exec(content)) !== null) {
        const [, categoryNumber, categoryName] = match;
        const cleanName = categoryName.trim();
        const path = `${categoryNumber}-${cleanName.toLowerCase().replace(/\s+/g, '-')}`;

        categories.push({
          name: cleanName,
          path: path,
          description: `Category ${categoryNumber}: ${cleanName}`,
        });
      }

      // If no categories found with regex, try alternative patterns
      if (categories.length === 0) {
        this.logger.debug("No categories found with regex, trying alternative parsing");
        // Look for directory references or other patterns
        const lines = content.split('\n');
        for (const line of lines) {
          if (line.includes('00-') || line.includes('01-') || line.includes('02-') || line.includes('03-')) {
            // Extract potential category references
            const categoryMatch = line.match(/(\d{2}-[^,\s\]]+)/);
            if (categoryMatch) {
              const categoryPath = categoryMatch[1];
              const categoryName = categoryPath.replace(/^\d{2}-/, '').replace(/-/g, ' ');
              categories.push({
                name: categoryName,
                path: categoryPath,
                description: `Category: ${categoryName}`,
              });
            }
          }
        }
      }

    } catch (error) {
      this.logger.error("Error parsing main.mdc content", error);
    }

    return categories;
  }

  private async fetchMainMdc(repository: GithubRepository): Promise<{ success: boolean; content?: string }> {
    try {
      // Try to fetch main.mdc from root
      const result = await this.githubService.fetchRepositoryContent(repository, "main.mdc");

      if (!result.success) {
        this.logger.debug("main.mdc not found in root");
        return { success: false };
      }

      const mainMdcFile = Array.isArray(result.data) ? result.data[0] : result.data;

      if (mainMdcFile.type !== "file" || !mainMdcFile.download_url) {
        this.logger.debug("main.mdc is not a file or has no download URL");
        return { success: false };
      }

      // Fetch the content
      const contentResult = await this.githubService.fetchFileContent(mainMdcFile.download_url);

      if (!contentResult.success) {
        this.logger.debug("Failed to fetch main.mdc content");
        return { success: false };
      }

      return { success: true, content: contentResult.data };
    } catch (error) {
      this.logger.debug(`Exception fetching main.mdc: ${error instanceof Error ? error.message : String(error)}`);
      return { success: false };
    }
  }

  private async discoverCategoryDirectories(repository: GithubRepository): Promise<CategoryInfo[]> {
    const categories: CategoryInfo[] = [];

    try {
      const result = await this.githubService.fetchRepositoryContent(repository, "");

      if (!result.success) {
        return categories;
      }

      const contents = Array.isArray(result.data) ? result.data : [result.data];

      for (const item of contents) {
        if (item.type === "dir" && /^\d{2}-/.test(item.name)) {
          const categoryName = item.name.replace(/^\d{2}-/, '').replace(/-/g, ' ');
          categories.push({
            name: categoryName,
            path: item.path,
            description: `Category: ${categoryName}`,
          });
        }
      }

      // Sort by category number
      categories.sort((a, b) => a.path.localeCompare(b.path));

    } catch (error) {
      this.logger.error("Error discovering category directories", error);
    }

    return categories;
  }
}
