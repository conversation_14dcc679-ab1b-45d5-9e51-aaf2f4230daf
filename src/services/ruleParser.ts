import {
    RuleCategory,
    RuleSystemError,
    type IRuleContent,
    type IRuleMetadata,
    type IRuleSystemError,
} from "../types/rules";
import type { ILogger } from "./logger";

/**
 * Interface for rule parsing service
 */
export interface IRuleParser {
  /**
   * Parse rule filename to extract metadata
   */
  parseRuleFilename(filename: string, service: string, path: string): IRuleMetadata | null;

  /**
   * Parse rule content from markdown
   */
  parseRuleContent(metadata: IRuleMetadata, content: string): IRuleContent;

  /**
   * Validate rule filename format
   */
  validateRuleFilename(filename: string): boolean;

  /**
   * Extract summary from rule content
   */
  extractSummary(content: string): string;
}

/**
 * Service for parsing rule files and extracting metadata
 */
export class RuleParser implements IRuleParser {
  private readonly logger: ILogger;

  // Rule filename pattern: ##-rule-name[@version][-specificity].mdc
  private readonly RULE_FILENAME_PATTERN = /^(\d{2})-([^@\-]+)(?:@([^-]+))?(?:-([^.]+))?\.mdc$/;

  constructor(logger: ILogger) {
    this.logger = logger;
  }

  /**
   * Parse rule filename to extract metadata
   */
  parseRuleFilename(filename: string, service: string, path: string): IRuleMetadata | null {
    try {
      const match = this.RULE_FILENAME_PATTERN.exec(filename);
      if (!match) {
        this.logger.warn(`Invalid rule filename format: ${filename}`);
        return null;
      }

      const [, categoryStr, name, version, specificity] = match;
      const categoryNum = parseInt(categoryStr, 10);

      // Validate category number
      if (categoryNum < 0 || categoryNum > 3) {
        this.logger.warn(`Invalid category number in filename: ${filename}`);
        return null;
      }

      const category = categoryNum as RuleCategory;

      const metadata: IRuleMetadata = {
        filename,
        category,
        name: this.cleanRuleName(name),
        path,
        service,
      };

      if (version) {
        metadata.version = version;
      }

      if (specificity) {
        metadata.specificity = specificity;
      }

      this.logger.debug(`Parsed rule metadata for ${filename}: ${JSON.stringify(metadata)}`);
      return metadata;
    } catch (error) {
      this.logger.error(`Error parsing rule filename ${filename}:`, error);
      return null;
    }
  }

  /**
   * Parse rule content from markdown
   */
  parseRuleContent(metadata: IRuleMetadata, content: string): IRuleContent {
    try {
      const summary = this.extractSummary(content);

      const ruleContent: IRuleContent = {
        ...metadata,
        content: content.trim(),
        summary,
        lastModified: new Date(),
      };

      this.logger.debug(`Parsed rule content for ${metadata.filename}, length: ${content.length}`);
      return ruleContent;
    } catch (error) {
      this.logger.error(`Error parsing rule content for ${metadata.filename}:`, error);
      throw this.createRuleSystemError(
        RuleSystemError.RULE_FILE_PARSE_ERROR,
        `Failed to parse rule content: ${metadata.filename}`,
        { metadata },
        error as Error
      );
    }
  }

  /**
   * Validate rule filename format
   */
  validateRuleFilename(filename: string): boolean {
    return this.RULE_FILENAME_PATTERN.test(filename);
  }

  /**
   * Extract summary from rule content
   */
  extractSummary(content: string): string {
    try {
      // Remove markdown headers and formatting
      const cleanContent = content
        .replace(/^#+\s*/gm, '') // Remove headers
        .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
        .replace(/\*(.*?)\*/g, '$1') // Remove italic
        .replace(/`(.*?)`/g, '$1') // Remove inline code
        .replace(/```[\s\S]*?```/g, '') // Remove code blocks
        .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
        .trim();

      // Get first paragraph or first 200 characters
      const firstParagraph = cleanContent.split('\n\n')[0];
      const summary = firstParagraph.length > 200
        ? firstParagraph.substring(0, 200) + '...'
        : firstParagraph;

      return summary || 'No summary available';
    } catch (error) {
      this.logger.warn(`Error extracting summary from content: ${error}`);
      return 'Summary extraction failed';
    }
  }

  /**
   * Clean rule name by replacing hyphens with spaces and capitalizing
   */
  private cleanRuleName(name: string): string {
    return name
      .replace(/-/g, ' ')
      .replace(/\b\w/g, (char) => char.toUpperCase());
  }

  /**
   * Create a structured rule system error
   */
  private createRuleSystemError(
    type: RuleSystemError,
    message: string,
    context?: Record<string, unknown>,
    originalError?: Error
  ): IRuleSystemError {
    const error = new Error(message) as IRuleSystemError;
    error.type = type;
    error.context = context;
    error.originalError = originalError;
    return error;
  }

  /**
   * Parse category number to RuleCategory enum
   */
  static parseCategoryNumber(categoryNum: number): RuleCategory | null {
    if (categoryNum >= 0 && categoryNum <= 3) {
      return categoryNum as RuleCategory;
    }
    return null;
  }

  /**
   * Get category display name
   */
  static getCategoryDisplayName(category: RuleCategory): string {
    const names = {
      [RuleCategory.ARCHITECTURE]: 'Architecture',
      [RuleCategory.WORKFLOWS]: 'Workflows',
      [RuleCategory.OPENAPI]: 'OpenAPI',
      [RuleCategory.DOMAIN_RULES]: 'Domain Rules',
    };
    return names[category];
  }

  /**
   * Format rule metadata for display
   */
  static formatRuleMetadata(metadata: IRuleMetadata): string {
    const parts = [
      `**${metadata.name}**`,
      `Category: ${RuleParser.getCategoryDisplayName(metadata.category)}`,
      `Service: ${metadata.service}`,
    ];

    if (metadata.version) {
      parts.push(`Version: ${metadata.version}`);
    }

    if (metadata.specificity) {
      parts.push(`Scope: ${metadata.specificity}`);
    }

    parts.push(`File: \`${metadata.filename}\``);

    return parts.join(' | ');
  }
}
