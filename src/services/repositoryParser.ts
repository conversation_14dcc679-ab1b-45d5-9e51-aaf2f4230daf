import type { IGitHubApiService } from "../api/github";
import {
    RuleCategory,
    RuleSystemError,
    type IRepositoryStructure,
    type IRuleMetadata,
    type IRuleSystemError,
    type IServiceMapping,
} from "../types/rules";
import type { ILogger } from "./logger";
import { RuleParser } from "./ruleParser";

/**
 * Interface for repository parsing service
 */
export interface IRepositoryParser {
  /**
   * Parse repository structure from main.mdc and discover rules
   */
  parseRepositoryStructure(owner: string, repo: string): Promise<IRepositoryStructure>;

  /**
   * Discover rule files in a service directory
   */
  discoverRuleFiles(owner: string, repo: string, servicePath: string): Promise<IRuleMetadata[]>;

  /**
   * Parse main.mdc file content
   */
  parseMainMdc(content: string): IRepositoryStructure;

  /**
   * Validate repository structure
   */
  validateRepositoryStructure(structure: IRepositoryStructure): boolean;
}

/**
 * Service for parsing repository structure and discovering rules
 */
export class RepositoryParser implements IRepositoryParser {
  private readonly logger: ILogger;
  private readonly githubService: IGitHubApiService;
  private readonly ruleParser: RuleParser;

  constructor(logger: ILogger, githubService: IGitHubApiService, ruleParser: RuleParser) {
    this.logger = logger;
    this.githubService = githubService;
    this.ruleParser = ruleParser;
  }

  /**
   * Parse repository structure from main.mdc and discover rules
   */
  async parseRepositoryStructure(owner: string, repo: string): Promise<IRepositoryStructure> {
    try {
      this.logger.info(`Parsing repository structure for ${owner}/${repo}`);

      // First, try to get main.mdc
      let structure: IRepositoryStructure;
      try {
        const repository = { owner, name: repo };
        const fileResult = await this.githubService.fetchRepositoryContent(repository, 'main.mdc');

        if (!fileResult.success || fileResult.data.length === 0) {
          throw new Error('main.mdc not found');
        }

        const file = fileResult.data[0];
        if (!file.download_url) {
          throw new Error('No download URL available for main.mdc');
        }

        const contentResult = await this.githubService.fetchFileContent(file.download_url);
        if (!contentResult.success) {
          throw contentResult.error;
        }

        structure = this.parseMainMdc(contentResult.data);
      } catch (error) {
        this.logger.warn(`main.mdc not found, discovering structure automatically`);
        structure = await this.discoverRepositoryStructure(owner, repo);
      }

      // Discover rules for each service
      for (const [serviceName, serviceMapping] of Object.entries(structure.services)) {
        try {
          const rules = await this.discoverRuleFiles(owner, repo, serviceName);
          this.updateServiceMappingWithRules(serviceMapping, rules);
        } catch (error) {
          this.logger.warn(`Failed to discover rules for service ${serviceName}: ${error}`);
        }
      }

      // Update total rule count
      structure.totalRules = Object.values(structure.services)
        .reduce((total, service) => total + Object.values(service.ruleCounts)
          .reduce((sum, count) => sum + count, 0), 0);

      structure.lastParsed = new Date();
      structure.repositoryUrl = `https://github.com/${owner}/${repo}`;

      this.logger.info(`Repository structure parsed successfully. Found ${structure.totalRules} rules across ${Object.keys(structure.services).length} services`);
      return structure;
    } catch (error) {
      this.logger.error(`Error parsing repository structure for ${owner}/${repo}:`, error);
      throw this.createRuleSystemError(
        RuleSystemError.MAIN_MDC_PARSE_ERROR,
        `Failed to parse repository structure: ${owner}/${repo}`,
        { owner, repo },
        error as Error
      );
    }
  }

  /**
   * Discover rule files in a service directory
   */
  async discoverRuleFiles(owner: string, repo: string, servicePath: string): Promise<IRuleMetadata[]> {
    try {
      this.logger.debug(`Discovering rule files in ${servicePath}`);

      const repository = { owner, name: repo };
      const result = await this.githubService.fetchRepositoryContent(repository, servicePath);

      if (!result.success) {
        throw result.error;
      }

      const files = result.data;
      const rules: IRuleMetadata[] = [];

      for (const file of files) {
        if (file.type === 'file' && file.name.endsWith('.mdc') && file.name !== 'main.mdc') {
          const metadata = this.ruleParser.parseRuleFilename(file.name, servicePath, file.path);
          if (metadata) {
            rules.push(metadata);
          }
        }
      }

      this.logger.debug(`Found ${rules.length} rule files in ${servicePath}`);
      return rules;
    } catch (error) {
      this.logger.error(`Error discovering rule files in ${servicePath}:`, error);
      throw this.createRuleSystemError(
        RuleSystemError.RULE_FILE_NOT_FOUND,
        `Failed to discover rule files in service: ${servicePath}`,
        { servicePath },
        error as Error
      );
    }
  }

  /**
   * Parse main.mdc file content
   */
  parseMainMdc(content: string): IRepositoryStructure {
    try {
      this.logger.debug('Parsing main.mdc content');

      const structure: IRepositoryStructure = {
        services: {},
        totalRules: 0,
        lastParsed: new Date(),
      };

      // Extract description from first paragraph
      const lines = content.split('\n');
      const descriptionLines: string[] = [];
      let inDescription = true;

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine && inDescription) {
          break;
        }
        if (trimmedLine && !trimmedLine.startsWith('#') && inDescription) {
          descriptionLines.push(trimmedLine);
        } else if (trimmedLine.startsWith('#')) {
          inDescription = false;
        }
      }

      if (descriptionLines.length > 0) {
        structure.description = descriptionLines.join(' ').trim();
      }

      // Parse service mappings (simplified - could be enhanced with more sophisticated parsing)
      const servicePattern = /##\s+([^#\n]+)/g;
      let match;
      while ((match = servicePattern.exec(content)) !== null) {
        const serviceName = match[1].trim().toLowerCase().replace(/\s+/g, '-');
        structure.services[serviceName] = {
          name: match[1].trim(),
          categories: [RuleCategory.ARCHITECTURE, RuleCategory.WORKFLOWS, RuleCategory.OPENAPI, RuleCategory.DOMAIN_RULES],
          ruleCounts: {
            [RuleCategory.ARCHITECTURE]: 0,
            [RuleCategory.WORKFLOWS]: 0,
            [RuleCategory.OPENAPI]: 0,
            [RuleCategory.DOMAIN_RULES]: 0,
          },
        };
      }

      this.logger.debug(`Parsed main.mdc: found ${Object.keys(structure.services).length} services`);
      return structure;
    } catch (error) {
      this.logger.error('Error parsing main.mdc content:', error);
      throw this.createRuleSystemError(
        RuleSystemError.MAIN_MDC_PARSE_ERROR,
        'Failed to parse main.mdc content',
        { contentLength: content.length },
        error as Error
      );
    }
  }

  /**
   * Validate repository structure
   */
  validateRepositoryStructure(structure: IRepositoryStructure): boolean {
    try {
      if (!structure.services || Object.keys(structure.services).length === 0) {
        return false;
      }

      for (const [serviceName, service] of Object.entries(structure.services)) {
        if (!service.name || !service.categories || !service.ruleCounts) {
          this.logger.warn(`Invalid service structure for ${serviceName}`);
          return false;
        }
      }

      return true;
    } catch (error) {
      this.logger.error('Error validating repository structure:', error);
      return false;
    }
  }

  /**
   * Discover repository structure automatically when main.mdc is not available
   */
  private async discoverRepositoryStructure(owner: string, repo: string): Promise<IRepositoryStructure> {
    this.logger.info('Discovering repository structure automatically');

    const structure: IRepositoryStructure = {
      description: `Auto-discovered structure for ${owner}/${repo}`,
      services: {},
      totalRules: 0,
      lastParsed: new Date(),
    };

    try {
      const repository = { owner, name: repo };
      const result = await this.githubService.fetchRepositoryContent(repository, '');

      if (result.success) {
        const rootContents = result.data;

        for (const item of rootContents) {
          if (item.type === 'dir' && !item.name.startsWith('.')) {
            structure.services[item.name] = {
              name: item.name,
              categories: [RuleCategory.ARCHITECTURE, RuleCategory.WORKFLOWS, RuleCategory.OPENAPI, RuleCategory.DOMAIN_RULES],
              ruleCounts: {
                [RuleCategory.ARCHITECTURE]: 0,
                [RuleCategory.WORKFLOWS]: 0,
                [RuleCategory.OPENAPI]: 0,
                [RuleCategory.DOMAIN_RULES]: 0,
              },
            };
          }
        }
      }
    } catch (error) {
      this.logger.warn(`Failed to auto-discover repository structure: ${error}`);
    }

    return structure;
  }

  /**
   * Update service mapping with discovered rules
   */
  private updateServiceMappingWithRules(serviceMapping: IServiceMapping, rules: IRuleMetadata[]): void {
    // Reset counts
    serviceMapping.ruleCounts = {
      [RuleCategory.ARCHITECTURE]: 0,
      [RuleCategory.WORKFLOWS]: 0,
      [RuleCategory.OPENAPI]: 0,
      [RuleCategory.DOMAIN_RULES]: 0,
    };

    // Count rules by category
    for (const rule of rules) {
      serviceMapping.ruleCounts[rule.category]++;
    }

    // Update available categories based on actual rules
    serviceMapping.categories = Object.entries(serviceMapping.ruleCounts)
      .filter(([, count]) => count > 0)
      .map(([category]) => parseInt(category, 10) as RuleCategory);
  }

  /**
   * Create a structured rule system error
   */
  private createRuleSystemError(
    type: RuleSystemError,
    message: string,
    context?: Record<string, unknown>,
    originalError?: Error
  ): IRuleSystemError {
    const error = new Error(message) as IRuleSystemError;
    error.type = type;
    error.context = context;
    error.originalError = originalError;
    return error;
  }
}
