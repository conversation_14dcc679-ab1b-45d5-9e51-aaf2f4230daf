import * as vscode from "vscode";
import type { IGitHubApiService } from "../api/github";
import type { GithubRepository } from "../api/types";
import type { ILogger } from "../services/logger";
import type { IStorageService } from "../services/storage";
import type { ExplorerView } from "../views/explorer/explorerView";
import type { ExplorerTreeItem } from "../views/explorer/treeItem";

interface CommandDependencies {
  context: vscode.ExtensionContext;
  explorerView: ExplorerView;
  githubService: IGitHubApiService;
  logger: ILogger;
  storageService: IStorageService;
  // Removed updatesTreeProvider from dependencies
}

export function registerCommands(dependencies: CommandDependencies): void {
  const { context, explorerView, logger, storageService } = dependencies; // Removed updatesTreeProvider from destructuring

  context.subscriptions.push(
    vscode.commands.registerCommand(
      "xendit.setRepository",
      (repository?: GithubRepository) => {
        if (repository) {
          explorerView.setRepository(repository).catch((error) => {
            logger.error("Error setting repository directly", error);

            vscode.window.showErrorMessage(
              `Error setting repository: ${error instanceof Error ? error.message : String(error)}`,
            );
          });
        } else {
          explorerView.promptForRepository();
        }
      },
    ), // Close the first push call correctly
  ); // End of the first push call

  // Keep the original setRepository registration.
  // ExplorerView.setRepository will be modified to handle updating UpdatesTreeProvider.

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.refresh", () => {
      explorerView.refreshView();
      // Keep refresh separate for now. User uses the specific refresh status button for updates.
    }),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand(
      "xendit.toggleSelection",
      (item: ExplorerTreeItem) => {
        explorerView.handleToggleSelectionCommand(item);
      },
    ),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand(
      "xendit.selectFolder",
      (item: ExplorerTreeItem) => {
        explorerView.handleSelectFolderCommand(item);
      },
    ),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand(
      "xendit.selectAllInFolder",
      (item: ExplorerTreeItem) => {
        explorerView.handleSelectAllInFolderCommand(item);
      },
    ),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand(
      "xendit.deselectAllInFolder",
      (item: ExplorerTreeItem) => {
        explorerView.handleDeselectAllInFolderCommand(item);
      },
    ),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.downloadSelected", () => {
      explorerView.downloadSelectedFiles();
    }),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.previewSelected", () => {
      explorerView.previewSelectedFiles();
    }),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.previewFile", (item: ExplorerTreeItem) => {
      explorerView.previewSingleFile(item);
    }),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.clearStorage", async () => {
      logger.debug("Clear storage command executed");
      const confirm = await vscode.window.showWarningMessage(
        "This will clear all Xendit Copilot storage, including recent repositories and settings. Are you sure?",
        { modal: true },
        "Yes",
      );
      if (confirm === "Yes") {
        storageService.clearStorage();
        vscode.window.showInformationMessage(
          "Xendit Copilot storage has been cleared.",
        );

        vscode.commands.executeCommand("xendit.refresh");
      }
    }),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.showOutput", () => {
      logger.show();
    }),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.openSettings", () => {
      logger.debug("Open settings command executed");
      vscode.commands.executeCommand(
        "workbench.action.openSettings",
        "@ext:xendit-copilot xendit.",
      );
    }),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.configureGitHubToken", async () => {
      logger.debug("Configure GitHub token command executed");

      const token = await vscode.window.showInputBox({
        prompt: "Enter your GitHub Personal Access Token",
        placeHolder: "ghp_1234567890abcdef...",
        password: true,
        ignoreFocusOut: true,
        validateInput: (value) => {
          if (!value || value.trim().length === 0) {
            return "GitHub token is required";
          }
          if (!value.startsWith("ghp_") && !value.startsWith("github_pat_")) {
            return "Invalid GitHub token format. Personal Access Tokens should start with 'ghp_' or 'github_pat_'";
          }
          return null;
        },
      });

      if (token) {
        const config = vscode.workspace.getConfiguration("xendit");
        await config.update("githubToken", token, vscode.ConfigurationTarget.Global);
        vscode.window.showInformationMessage(
          "GitHub token configured successfully! You can now access private repositories."
        );
        logger.info("GitHub token configured by user");
      }
    }),
  );

  // Register the new command to check for rule updates
  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.refreshRuleStatus", () => {
      logger.debug("Refresh rule status command executed");
      // Only refresh the main explorer view (which now handles status)
      explorerView.treeProvider.refreshAndUpdateStatus().catch((error) => {
        logger.error("Error executing refreshRuleStatus command", error);
        // Error is handled and shown by the method itself
      });
    }),
  );
}
