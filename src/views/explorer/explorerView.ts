import * as vscode from "vscode";
import type { IGitHubApiService } from "../../api/github";
import type { GithubRepository } from "../../api/types";
import type { DownloadFile, IDownloadService } from "../../services/download";
import type { IExplorerStateService } from "../../services/explorerStateService";
import type { ILogger } from "../../services/logger";
import type { IPreviewService } from "../../services/previewService";
import type { ISelectionService } from "../../services/selection";
import type { IStatusBarService } from "../../services/statusBarService";
import type { IStorageService } from "../../services/storage";
import type { IUpdateCheckService } from "../../services/updateCheckService";
import type { IXenditKnowledgeService } from "../../services/xenditKnowledgeService";
// Removed import for UpdatesTreeProvider
import { parseRepositoryUrl } from "../../utils/githubUtils";
import type { ExplorerTreeItem } from "./treeItem";
import { ExplorerTreeProvider } from "./treeProvider";

export class ExplorerView {
  public static readonly VIEW_ID = "xendit.explorerView";

  public readonly treeProvider: ExplorerTreeProvider; // Make public to access from extension.ts
  private treeView!: vscode.TreeView<ExplorerTreeItem>;
  private currentRepository: GithubRepository | null = null;

  constructor(
    private readonly context: vscode.ExtensionContext,
    private readonly githubService: IGitHubApiService,
    private readonly logger: ILogger,
    private readonly storageService: IStorageService,
    private readonly downloadService: IDownloadService,
    private readonly selectionService: ISelectionService,
    private readonly stateService: IExplorerStateService,
    private readonly updateCheckService: IUpdateCheckService,
    private readonly statusBarService: IStatusBarService,
    private readonly xenditKnowledgeService: IXenditKnowledgeService,
    private readonly previewService: IPreviewService,
    // Removed updatesTreeProvider from constructor params
  ) {
    this.treeProvider = new ExplorerTreeProvider(
      githubService,
      logger,
      selectionService,
      stateService,
      updateCheckService,
      statusBarService, // Pass StatusBarService
      xenditKnowledgeService, // Pass XenditKnowledgeService
      context,
      this.context.extensionPath,
    );
    // Store the provider passed from extension.ts
    // this.updatesTreeProvider = updatesTreeProvider; // Already done via constructor param

    this.treeView = vscode.window.createTreeView(ExplorerView.VIEW_ID, {
      treeDataProvider: this.treeProvider,
      showCollapseAll: true,
      canSelectMany: false,
    });

    this.restoreLastRepository();

    this.registerViewListeners();
  }

  /** Register listeners specific to the TreeView UI elements */
  private registerViewListeners(): void {
    this.context.subscriptions.push(
      this.treeView.onDidChangeVisibility((e) => {
        if (e.visible) {
          this.logger.debug("Xendit Copilot view became visible");
        }
      }),
    );

    try {
      const view = this.treeView as any;

      if (typeof view.onDidChangeCheckboxState !== "undefined") {
        this.context.subscriptions.push(
          view.onDidChangeCheckboxState(
            (e: vscode.TreeCheckboxChangeEvent<ExplorerTreeItem>) => {
              for (const [item, state] of e.items) {
                const checked = state === vscode.TreeItemCheckboxState.Checked;

                this.treeProvider.handleCheckboxChange(item, checked);
              }
            },
          ),
        );
      } else {
        this.logger.warn(
          "TreeView checkbox API not available. Using fallback command/icons for selection.",
        );
      }
    } catch (e) {
      this.logger.error(
        "Error setting up checkbox listener, relying on fallback.",
        e,
      );
    }
  }

  /** Restore last repository from storage */
  private async restoreLastRepository(): Promise<void> {
    const lastRepo = this.storageService.getLastRepository();
    if (lastRepo) {
      try {
        await this.setRepository(lastRepo);
        this.logger.info(
          `Restored last repository: ${lastRepo.owner}/${lastRepo.name}`,
        );
      } catch (error) {
        this.logger.error("Failed to restore last repository", error);
        vscode.window.showErrorMessage(
          `Failed to restore last repository (${lastRepo.owner}/${
            lastRepo.name
          }): ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }
  }

  /** Prompts user to select or enter a repository URL */
  public async promptForRepository(): Promise<void> {
    this.logger.debug("ExplorerView promptForRepository called.");

    this.selectionService.clearSelection();

    const featuredRepo: GithubRepository = {
      owner: "sammous",
      name: "xendit-knowledge",
      branch: "",
    };
    const featuredRepoId = `${featuredRepo.owner}/${featuredRepo.name}`;

    const storedRepos = this.storageService.getRecentRepositories();
    const items: (vscode.QuickPickItem & { repo?: GithubRepository })[] = [
      {
        label: "$(repo) Enter repository URL...",
        description: "Specify a GitHub repository URL",
      },

      {
        label: `$(star-full) ${featuredRepo.owner}/${featuredRepo.name}`,
        description: "Featured repository",
        repo: featuredRepo,
      },
    ];

    for (const repo of storedRepos) {
      const repoId = `${repo.owner}/${repo.name}`;
      if (repoId !== featuredRepoId) {
        items.push({
          label: `$(history) ${repo.owner}/${repo.name}`,
          description: repo.branch
            ? `Branch: ${repo.branch}`
            : "Default branch",
          repo,
        });
      }
    }

    const selection = await vscode.window.showQuickPick(items, {
      placeHolder: "Select a featured or recent repository, or enter a URL",
      matchOnDescription: true,
    });

    if (!selection) {
      return;
    }

    if (selection.repo) {
      try {
        await this.setRepository(selection.repo);
      } catch (error) {
        this.logger.error(
          `Failed to set recent repository: ${selection.repo.owner}/${selection.repo.name}`,
          error,
        );
        vscode.window.showErrorMessage(
          `Failed to load recent repository: ${
            error instanceof Error ? error.message : String(error)
          }`,
        );
      }
    } else {
      const repoUrl = await vscode.window.showInputBox({
        prompt: "Enter GitHub repository URL",
        placeHolder: "https://github.com/owner/repo",
        value: "https://github.com/sammous/xendit-knowledge",
        validateInput: (value) => {
          if (!value) {
            return "Repository URL is required";
          }
          const repo = parseRepositoryUrl(value);
          if (!repo) {
            return "Invalid GitHub repository URL";
          }
          return null;
        },
      });
      if (!repoUrl) {
        return;
      }
      const repo = parseRepositoryUrl(repoUrl);
      if (repo) {
        await this.setRepository(repo);
      }
    }
  }

  /** Sets the repository for the view and triggers data loading */
  public async setRepository(repository: GithubRepository): Promise<void> {
    try {
      this.treeView.title = `GitHub: ${repository.owner}/${repository.name}${
        repository.branch ? ` (${repository.branch})` : ""
      }`;

      this.selectionService.clearSelection();
      await this.treeProvider.setRepository(repository);
      this.storageService.addRecentRepository(repository);
      // Removed call to updatesTreeProvider.setCurrentRepository
      vscode.window.showInformationMessage(
        `Connected to GitHub repository: ${repository.owner}/${repository.name}`,
      );
      // Optionally trigger initial status check after setting repo
      // vscode.commands.executeCommand("xendit.refreshRuleStatus");

      vscode.commands.executeCommand(`${ExplorerView.VIEW_ID}.focus`);
    } catch (error) {
      // Removed call to updatesTreeProvider.setCurrentRepository
      this.logger.error(
        `Error connecting to repository: ${repository.owner}/${repository.name}`,
        error,
      );
      vscode.window.showErrorMessage(
        `Error connecting to repository: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      throw error;
    }
  }

  /** Refreshes the entire tree view */
  public refreshView(): void {
    this.logger.debug("ExplorerView refreshView called.");

    this.treeProvider.refresh();
  }

  /** Resets the view completely (used when clearing storage) */
  public resetView(): void {
    this.logger.debug("ExplorerView resetView called.");

    // Clear current repository reference
    this.currentRepository = null;

    // Reset tree view title
    this.treeView.title = "Xendit Copilot";

    // Clear selection
    this.selectionService.clearSelection();

    // Reset tree provider state completely
    this.treeProvider.resetState();

    this.logger.info("Explorer view has been reset.");
  }

  /** Handles the toggle selection command */
  public handleToggleSelectionCommand(item: ExplorerTreeItem): void {
    if (item?.content?.path) {
      this.logger.debug(
        `Command xendit.toggleSelection triggered for: ${item.content.path}`,
      );

      const isCurrentlySelected = this.selectionService.isSelected(
        item.content.path,
      );
      const targetCheckedState = !isCurrentlySelected;

      this.treeProvider
        .handleCheckboxChange(item, targetCheckedState)
        .catch((err) => {
          this.logger.error(
            `Error during handleCheckboxChange for ${item.content.path}`,
            err,
          );
          vscode.window.showErrorMessage(
            `Failed to toggle selection: ${err instanceof Error ? err.message : String(err)}`,
          );
        });
    } else {
      this.logger.warn("toggleSelection command called without a valid item.");
    }
  }

  /** Handles the select folder command */
  public handleSelectFolderCommand(item: ExplorerTreeItem): void {
    if (item?.content?.path && item.content.type === "dir") {
      this.logger.debug(
        `Command xendit.selectFolder triggered for folder: ${item.content.path}`,
      );

      // Get current selection info before toggling
      const beforeInfo = this.selectionService.getFolderSelectionInfo(item.content.path);
      const wasFullySelected = beforeInfo.selectedItems === beforeInfo.totalItems;

      // Use recursive selection to select/deselect the folder and all its contents
      this.selectionService.toggleRecursiveSelection(item.content.path);

      // Get selection info after toggling
      const afterInfo = this.selectionService.getFolderSelectionInfo(item.content.path);
      const isNowFullySelected = afterInfo.selectedItems === afterInfo.totalItems;

      // Update the tree view to reflect the selection changes
      this.treeProvider.refresh();

      // Show appropriate confirmation message
      if (isNowFullySelected && !wasFullySelected) {
        vscode.window.showInformationMessage(
          `Selected folder "${item.content.name}" with ${afterInfo.totalItems} items.`
        );
      } else if (!isNowFullySelected && wasFullySelected) {
        vscode.window.showInformationMessage(
          `Deselected folder "${item.content.name}" and its ${afterInfo.totalItems} items.`
        );
      } else {
        vscode.window.showInformationMessage(
          `Toggled selection for folder "${item.content.name}" (${afterInfo.selectedItems}/${afterInfo.totalItems} items selected).`
        );
      }
    } else {
      this.logger.warn("selectFolder command called without a valid directory item.");
      vscode.window.showWarningMessage("Please select a valid folder.");
    }
  }

  /** Handles the select all in folder command */
  public handleSelectAllInFolderCommand(item: ExplorerTreeItem): void {
    if (item?.content?.path && item.content.type === "dir") {
      this.logger.debug(
        `Command xendit.selectAllInFolder triggered for folder: ${item.content.path}`,
      );

      // Get all items in the folder
      const allItemsMap = this.stateService.getAllItems();
      const prefix = item.content.path === "" ? "" : `${item.content.path}/`;
      const itemsToSelect: string[] = [];

      // Add the folder itself
      itemsToSelect.push(item.content.path);

      // Add all items within the folder
      for (const itemInMap of allItemsMap.values()) {
        if (
          itemInMap.content.path !== item.content.path &&
          (item.content.path === "" ? true : itemInMap.content.path.startsWith(prefix))
        ) {
          itemsToSelect.push(itemInMap.content.path);
        }
      }

      // Select all items
      this.selectionService.selectItems(itemsToSelect);

      // Update the tree view to reflect the selection changes
      this.treeProvider.refresh();

      // Show confirmation message
      vscode.window.showInformationMessage(
        `Selected all ${itemsToSelect.length} items in folder "${item.content.name}".`
      );
    } else {
      this.logger.warn("selectAllInFolder command called without a valid directory item.");
      vscode.window.showWarningMessage("Please select a valid folder.");
    }
  }

  /** Handles the deselect all in folder command */
  public handleDeselectAllInFolderCommand(item: ExplorerTreeItem): void {
    if (item?.content?.path && item.content.type === "dir") {
      this.logger.debug(
        `Command xendit.deselectAllInFolder triggered for folder: ${item.content.path}`,
      );

      // Get all items in the folder
      const allItemsMap = this.stateService.getAllItems();
      const prefix = item.content.path === "" ? "" : `${item.content.path}/`;
      const itemsToDeselect: string[] = [];

      // Add the folder itself
      itemsToDeselect.push(item.content.path);

      // Add all items within the folder
      for (const itemInMap of allItemsMap.values()) {
        if (
          itemInMap.content.path !== item.content.path &&
          (item.content.path === "" ? true : itemInMap.content.path.startsWith(prefix))
        ) {
          itemsToDeselect.push(itemInMap.content.path);
        }
      }

      // Deselect all items
      this.selectionService.deselectItems(itemsToDeselect);

      // Update the tree view to reflect the selection changes
      this.treeProvider.refresh();

      // Show confirmation message
      vscode.window.showInformationMessage(
        `Deselected all ${itemsToDeselect.length} items in folder "${item.content.name}".`
      );
    } else {
      this.logger.warn("deselectAllInFolder command called without a valid directory item.");
      vscode.window.showWarningMessage("Please select a valid folder.");
    }
  }

  /** Handles the download selected files command */
  public async downloadSelectedFiles(): Promise<void> {
    this.logger.debug("ExplorerView downloadSelectedFiles called.");
    try {
      const selectedPaths = this.selectionService.getSelectedItems();

      if (selectedPaths.length === 0) {
        vscode.window.showInformationMessage(
          "No items selected. Use the checkbox to select files or directories to download.",
        );
        return;
      }

      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        vscode.window.showErrorMessage(
          "No workspace folder open. Please open a folder to download files.",
        );
        return;
      }
      const workspaceFolder = workspaceFolders[0].uri.fsPath;

      const selectedItems =
        await this.treeProvider.getTreeItemsByPaths(selectedPaths);

      if (!selectedItems || selectedItems.length === 0) {
        vscode.window.showErrorMessage(
          "Could not retrieve details for selected items.",
        );
        this.logger.error("Failed to get TreeItems for selected paths", {
          selectedPaths,
        });
        return;
      }

      const itemsToProcess: DownloadFile[] = selectedItems
        .map((item): DownloadFile | null => {
          if (!item?.content?.path || !item.content.type) {
            this.logger.warn(
              `Skipping invalid selected item in download mapping: ${JSON.stringify(
                item,
              )}`,
            );
            return null;
          }

          if (item.content.type === "dir") {
            return {
              targetPath: item.content.path,
              type: "dir",
            };
          }
          if (item.content.type === "file") {
            return {
              targetPath: item.content.path,
              type: "file",
              size: item.content.size,
              sha: item.content.sha, // Add the SHA property here

              downloadUrl: item.content.download_url,
              base64Content: item.content.content,
            };
          }

          this.logger.warn(
            `Skipping item with unexpected type '${item.content.type}': ${item.content.path}`,
          );
          return null;
        })
        .filter((item): item is DownloadFile => item !== null);

      if (itemsToProcess.length === 0) {
        vscode.window.showInformationMessage(
          "No downloadable files or directories selected, or failed to retrieve item details.",
        );
        return;
      }

      const currentRepo = this.treeProvider.getCurrentRepository();
      if (!currentRepo) {
        vscode.window.showErrorMessage(
          "No repository is currently loaded. Cannot download files.",
        );
        this.logger.error(
          "downloadSelectedFiles called but no repository is set in TreeProvider.",
        );
        return;
      }

      const results = await this.downloadService.downloadFiles(
        itemsToProcess,
        workspaceFolder,
        currentRepo,
      );

      const successCount = results.filter((r) => r.success).length;
      const failCount = results.filter((r) => !r.success).length;

      if (failCount === 0) {
        vscode.window.showInformationMessage(
          `Successfully downloaded ${successCount} items.`,
        );
      } else {
        vscode.window.showWarningMessage(
          `Downloaded ${successCount} items with ${failCount} errors. Check the output log for details.`,
        );
      }
    } catch (error) {
      this.logger.error("Error downloading files", error);
      vscode.window.showErrorMessage(
        `Error downloading files: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  /** Handles the preview selected files command */
  public async previewSelectedFiles(): Promise<void> {
    this.logger.debug("ExplorerView previewSelectedFiles called.");
    try {
      const selectedPaths = this.selectionService.getSelectedItems();

      if (selectedPaths.length === 0) {
        vscode.window.showInformationMessage(
          "No files selected for preview. Use the checkbox to select files."
        );
        return;
      }

      const currentRepo = this.treeProvider.getCurrentRepository();
      if (!currentRepo) {
        vscode.window.showErrorMessage(
          "No repository is currently loaded. Cannot preview files."
        );
        this.logger.error(
          "previewSelectedFiles called but no repository is set in TreeProvider."
        );
        return;
      }

      // Filter out directories - only preview files
      const selectedItems = await this.treeProvider.getTreeItemsByPaths(selectedPaths);
      const filePaths = selectedItems
        .filter(item => item?.content?.type === "file")
        .map(item => item.content!.path);

      if (filePaths.length === 0) {
        vscode.window.showInformationMessage(
          "No files selected for preview. Only files can be previewed, not directories."
        );
        return;
      }

      if (filePaths.length !== selectedPaths.length) {
        const dirCount = selectedPaths.length - filePaths.length;
        vscode.window.showInformationMessage(
          `Skipping ${dirCount} director${dirCount === 1 ? 'y' : 'ies'}. Only files can be previewed.`
        );
      }

      // Use the preview service to open files
      await this.previewService.previewFiles(filePaths, currentRepo);

      this.logger.info(`Successfully initiated preview for ${filePaths.length} files`);

    } catch (error) {
      this.logger.error("Error previewing files", error);
      vscode.window.showErrorMessage(
        `Error previewing files: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /** Handles the preview single file command */
  public async previewSingleFile(item: ExplorerTreeItem): Promise<void> {
    this.logger.debug(`ExplorerView previewSingleFile called for: ${item.content.path}`);

    try {
      // Only preview files, not directories
      if (item.content.type !== "file") {
        vscode.window.showInformationMessage(
          "Only files can be previewed, not directories."
        );
        return;
      }

      const currentRepo = this.treeProvider.getCurrentRepository();
      if (!currentRepo) {
        vscode.window.showErrorMessage(
          "No repository is currently loaded. Cannot preview file."
        );
        this.logger.error(
          "previewSingleFile called but no repository is set in TreeProvider."
        );
        return;
      }

      // Check if file type is supported for preview
      if (!this.previewService.isSupportedFileType(item.content.path)) {
        vscode.window.showWarningMessage(
          `File type not supported for preview: ${item.content.path}`
        );
        return;
      }

      // Use the preview service to open the file
      await this.previewService.previewSingleFile(item.content.path, currentRepo);

      this.logger.info(`Successfully initiated preview for file: ${item.content.path}`);

    } catch (error) {
      this.logger.error("Error previewing file", error);
      vscode.window.showErrorMessage(
        `Error previewing file: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
