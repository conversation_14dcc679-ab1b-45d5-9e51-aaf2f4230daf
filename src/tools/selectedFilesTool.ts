import * as vscode from "vscode";
import type { IGitHubApiService } from "../api/github";
import type { ILogger } from "../services/logger";
import type { ISelectionService } from "../services/selection";
import type { IStorageService } from "../services/storage";
import type { IFileData } from "../prompts";

/**
 * Parameters for the selected files tool
 */
export interface ISelectedFilesToolParams {
  /**
   * Maximum number of files to include (default: 10)
   */
  maxFiles?: number;

  /**
   * Whether to include file content or just file paths (default: true)
   */
  includeContent?: boolean;

  /**
   * Maximum content length per file in characters (default: 10000)
   */
  maxContentLength?: number;
}

/**
 * Tool for retrieving content of selected files from the explorer view
 */
export class SelectedFilesTool implements vscode.LanguageModelTool<ISelectedFilesToolParams> {
  private readonly logger: ILogger;
  private readonly selectionService: ISelectionService;
  private readonly githubService: IGitHubApiService;
  private readonly storageService: IStorageService;

  constructor(
    logger: ILogger,
    selectionService: ISelectionService,
    githubService: IGitHubApiService,
    storageService: IStorageService
  ) {
    this.logger = logger;
    this.selectionService = selectionService;
    this.githubService = githubService;
    this.storageService = storageService;
  }

  /**
   * Invoke the selected files tool
   */
  async invoke(
    options: vscode.LanguageModelToolInvocationOptions<ISelectedFilesToolParams>,
    token: vscode.CancellationToken
  ): Promise<vscode.LanguageModelToolResult> {
    try {
      this.logger.info("Selected files tool invoked");

      // Get parameters with defaults
      const maxFiles = options.input.maxFiles || 10;
      const includeContent = options.input.includeContent !== false;
      const maxContentLength = options.input.maxContentLength || 10000;

      // Get selected files
      const selectedPaths = this.selectionService.getSelectedItems();

      if (selectedPaths.length === 0) {
        return new vscode.LanguageModelToolResult([
          new vscode.LanguageModelTextPart("No files are currently selected in the explorer view.")
        ]);
      }

      // Get current repository
      const repository = this.storageService.getLastRepository();
      if (!repository) {
        return new vscode.LanguageModelToolResult([
          new vscode.LanguageModelTextPart("No repository is currently loaded.")
        ]);
      }

      // Limit number of files
      const filesToProcess = selectedPaths.slice(0, maxFiles);

      if (selectedPaths.length > maxFiles) {
        this.logger.warn(`Limiting selected files from ${selectedPaths.length} to ${maxFiles}`);
      }

      // Check for cancellation
      if (token.isCancellationRequested) {
        return new vscode.LanguageModelToolResult([
          new vscode.LanguageModelTextPart("Selected files retrieval was cancelled.")
        ]);
      }

      // For tools, we return text content only
      // The prompt-tsx components can be used by chat participants
      return await this.createFallbackResult(
        repository,
        filesToProcess,
        includeContent,
        maxContentLength,
        token
      );

    } catch (error) {
      this.logger.error("Error in selected files tool:", error);
      return new vscode.LanguageModelToolResult([
        new vscode.LanguageModelTextPart(`❌ Error retrieving selected files: ${error instanceof Error ? error.message : String(error)}`)
      ]);
    }
  }

  /**
   * Prepare tool invocation with user confirmation
   */
  async prepareInvocation(
    options: vscode.LanguageModelToolInvocationPrepareOptions<ISelectedFilesToolParams>,
    _token: vscode.CancellationToken
  ): Promise<vscode.PreparedToolInvocation> {
    const selectedPaths = this.selectionService.getSelectedItems();
    const maxFiles = options.input.maxFiles || 10;
    const includeContent = options.input.includeContent !== false;

    const filesToProcess = Math.min(selectedPaths.length, maxFiles);

    let confirmationMessage = `Retrieve ${filesToProcess} selected file${filesToProcess !== 1 ? 's' : ''}`;

    if (includeContent) {
      confirmationMessage += " with content";
    } else {
      confirmationMessage += " (paths only)";
    }

    if (selectedPaths.length > maxFiles) {
      confirmationMessage += ` (limited from ${selectedPaths.length} files)`;
    }

    const confirmationMessages = {
      title: "Get Selected Files",
      message: new vscode.MarkdownString(confirmationMessage)
    };

    return {
      invocationMessage: includeContent ? "Fetching selected files content..." : "Getting selected file paths...",
      confirmationMessages
    };
  }

  /**
   * Get prompt-tsx components for chat participants
   * This method can be used by chat participants to get rich prompt elements
   */
  async getPromptComponents(
    repository: any,
    filesToProcess: string[],
    maxContentLength: number,
    token: vscode.CancellationToken
  ): Promise<{ files: IFileData[]; repository: any }> {
    const files = await this.fetchFileData(filesToProcess, repository, maxContentLength, token);
    return { files, repository };
  }

  /**
   * Create fallback text for compatibility
   */
  private async createFallbackText(
    repository: any,
    filesToProcess: string[],
    includeContent: boolean,
    maxContentLength: number,
    token: vscode.CancellationToken
  ): Promise<string> {
    let result = `## Selected Files (${filesToProcess.length})\n\n`;
    result += `Repository: **${repository.owner}/${repository.name}** (branch: ${repository.branch})\n\n`;

    if (!includeContent) {
      result += "### File Paths:\n";
      for (const filePath of filesToProcess) {
        result += `- \`${filePath}\`\n`;
      }
      return result;
    }

    const files = await this.fetchFileData(filesToProcess, repository, maxContentLength, token);

    for (const file of files) {
      result += `### \`${file.path}\`\n\n`;

      if (file.error) {
        result += `❌ **Error:** ${file.error}\n\n`;
      } else if (file.content.trim()) {
        const language = file.language || 'text';
        result += `\`\`\`${language}\n${file.content}\n\`\`\`\n\n`;
      } else {
        result += "*File is empty*\n\n";
      }
    }

    return result;
  }

  /**
   * Create fallback result using original implementation
   */
  private async createFallbackResult(
    repository: any,
    filesToProcess: string[],
    includeContent: boolean,
    maxContentLength: number,
    token: vscode.CancellationToken
  ): Promise<vscode.LanguageModelToolResult> {
    const fallbackText = await this.createFallbackText(
      repository,
      filesToProcess,
      includeContent,
      maxContentLength,
      token
    );

    return new vscode.LanguageModelToolResult([
      new vscode.LanguageModelTextPart(fallbackText)
    ]);
  }

  /**
   * Fetch file data from GitHub API
   */
  private async fetchFileData(
    filePaths: string[],
    repository: any,
    maxContentLength: number,
    token: vscode.CancellationToken
  ): Promise<IFileData[]> {
    const files: IFileData[] = [];

    for (const filePath of filePaths) {
      if (token.isCancellationRequested) {
        break;
      }

      try {
        this.logger.debug(`Fetching content for: ${filePath}`);

        // Get file content from GitHub
        const contentResult = await this.githubService.fetchRepositoryContent(repository, filePath);

        if (!contentResult.success || contentResult.data.length === 0) {
          files.push({
            path: filePath,
            content: "",
            error: "File not found or empty",
            language: this.getLanguageFromExtension(filePath.split('.').pop()?.toLowerCase() || '')
          });
          continue;
        }

        const file = contentResult.data[0];
        if (file.type !== "file" || !file.download_url) {
          files.push({
            path: filePath,
            content: "",
            error: "Not a file or no download URL available",
            language: this.getLanguageFromExtension(filePath.split('.').pop()?.toLowerCase() || '')
          });
          continue;
        }

        const fileContentResult = await this.githubService.fetchFileContent(file.download_url);
        if (!fileContentResult.success) {
          files.push({
            path: filePath,
            content: "",
            error: `Failed to fetch content: ${fileContentResult.error?.message || "Unknown error"}`,
            language: this.getLanguageFromExtension(filePath.split('.').pop()?.toLowerCase() || '')
          });
          continue;
        }

        let content = fileContentResult.data;

        // Truncate content if too long
        if (content.length > maxContentLength) {
          content = content.substring(0, maxContentLength) + "\n\n... (content truncated)";
        }

        files.push({
          path: filePath,
          content: content,
          language: this.getLanguageFromExtension(filePath.split('.').pop()?.toLowerCase() || '')
        });

      } catch (error) {
        this.logger.error(`Error fetching content for ${filePath}:`, error);
        files.push({
          path: filePath,
          content: "",
          error: error instanceof Error ? error.message : String(error),
          language: this.getLanguageFromExtension(filePath.split('.').pop()?.toLowerCase() || '')
        });
      }
    }

    return files;
  }

  /**
   * Get language identifier from file extension for syntax highlighting
   */
  private getLanguageFromExtension(extension: string): string {
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'ts': 'typescript',
      'jsx': 'javascript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'kt': 'kotlin',
      'swift': 'swift',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'toml': 'toml',
      'ini': 'ini',
      'cfg': 'ini',
      'conf': 'ini',
      'sh': 'bash',
      'bash': 'bash',
      'zsh': 'bash',
      'fish': 'bash',
      'ps1': 'powershell',
      'sql': 'sql',
      'md': 'markdown',
      'txt': 'text',
      'log': 'text'
    };

    return languageMap[extension] || 'text';
  }
}
