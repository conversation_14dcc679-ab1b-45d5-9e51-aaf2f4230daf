{"name": "xendit-copilot", "displayName": "Xendit Copilot", "description": "VS Code extension that integrates with GitHub Copilot Chat to provide custom context from synced GitHub repositories with intelligent rule retrieval", "version": "0.0.4", "icon": "assets/xendit.png", "publisher": "xendit", "engines": {"vscode": "^1.96.0"}, "repository": {"type": "git", "url": "https://github.com/sammous/xendit-knowledge"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"languageModelTools": [{"name": "xendit-copilot_contextRetrieval", "displayName": "Automatically retrieve context from knowledge repository", "toolReferenceName": "XenditCopilotAddContext", "canBeReferencedInPrompt": true, "modelDescription": "Retrieve relevant context and knowledge from a central repository to help answer questions and provide information", "tags": ["knowledge", "context", "xendit-copilot", "chat-tools-sample"], "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "User query for context retrieval (e.g., 'payment processing', 'user authentication', 'API documentation')"}, "maxResults": {"type": "number", "description": "Maximum number of results to retrieve (default: 5)", "minimum": 1, "maximum": 20}, "service": {"type": "string", "description": "Specific service to search in (optional)"}, "includeContent": {"type": "boolean", "description": "Include full content in response (default: true)"}}, "required": ["query"]}}, {"name": "xendit-copilot_selectedFiles", "displayName": "Add selected files to your chat context", "toolReferenceName": "XenditCopilotGetSelectedFiles", "canBeReferencedInPrompt": true, "modelDescription": "Retrieve content of files currently selected in the repository explorer view with intelligent context management", "tags": ["files", "context", "selected", "xendit-copilot"], "inputSchema": {"type": "object", "properties": {"maxFiles": {"type": "number", "description": "Maximum number of files to include (default: 10)", "minimum": 1, "maximum": 50}, "includeContent": {"type": "boolean", "description": "Whether to include file content or just file paths (default: true)"}, "maxContentLength": {"type": "number", "description": "Maximum content length per file in characters (default: 10000)", "minimum": 100, "maximum": 50000}}, "required": []}}], "commands": [{"command": "xendit.setRepository", "title": "Xendit Copilot: Set Repository", "icon": "$(repo)"}, {"command": "xendit.refresh", "title": "Xendit Copilot: Refresh", "icon": "$(refresh)"}, {"command": "xendit.toggleSelection", "title": "Xendit Copilot: Toggle Selection", "icon": "$(check)"}, {"command": "xendit.downloadSelected", "title": "Xendit Copilot: Download Selected Files", "icon": "$(cloud-download)"}, {"command": "xendit.previewSelected", "title": "Xendit Copilot: Preview Selected Files", "icon": "$(eye)"}, {"command": "xendit.previewFile", "title": "Xendit Copilot: Preview File", "icon": "$(eye)"}, {"command": "xendit.openSettings", "title": "Xendit Copilot: Open Settings", "icon": "$(gear)"}, {"command": "xendit.clearStorage", "title": "Xendit Copilot: Clear Storage", "icon": "$(trash)"}, {"command": "xendit.refreshRuleStatus", "title": "Xendit Copilot: Check for Updates", "icon": "$(sync)"}, {"command": "xendit.configureGitHubToken", "title": "Xendit Copilot: Configure GitHub Token", "icon": "$(key)"}, {"command": "xendit.selectFolder", "title": "Xendit Copilot: Select Folder", "icon": "$(folder-active)"}, {"command": "xendit.selectAllInFolder", "title": "Xendit Copilot: Select All in Folder", "icon": "$(select-all)"}, {"command": "xendit.deselectAllInFolder", "title": "Xendit Copilot: Deselect All in Folder", "icon": "$(close-all)"}], "viewsContainers": {"activitybar": [{"id": "xendit-copilot", "title": "Xendit Copilot", "icon": "$(xendit-logo)"}]}, "views": {"xendit-copilot": [{"id": "xendit.welcomeView", "name": "Get Started", "type": "webview", "visibility": "visible"}, {"id": "xendit.explorer<PERSON>iew", "name": "Repository Explorer", "icon": "$(xendit-logo)", "contextualTitle": "Xendit Copilot"}]}, "menus": {"view/title": [{"command": "xendit.setRepository", "when": "view == xendit.explorerView", "group": "navigation"}, {"command": "xendit.refresh", "when": "view == xendit.explorerView", "group": "navigation"}, {"command": "xendit.downloadSelected", "when": "view == xendit.explorerView", "group": "navigation"}, {"command": "xendit.refreshRuleStatus", "when": "view == xendit.explorerView", "group": "navigation@3"}, {"command": "xendit.openSettings", "when": "view == xendit.explorerView", "group": "navigation@5"}, {"command": "xendit.configureGitHubToken", "when": "view == xendit.explorerView", "group": "navigation@6"}], "view/item/context": [{"command": "xendit.previewFile", "when": "view == xendit.explorerView && viewItem == file", "group": "inline@1"}, {"command": "xendit.toggleSelection", "when": "view == xendit.explorerView && viewItem == file", "group": "inline@2"}, {"command": "xendit.selectFolder", "when": "view == xendit.explorerView && viewItem == directory", "group": "inline@1"}, {"command": "xendit.toggleSelection", "when": "view == xendit.explorerView && viewItem == directory", "group": "inline@2"}, {"command": "xendit.selectAllInFolder", "when": "view == xendit.explorerView && viewItem == directory", "group": "context@1"}, {"command": "xendit.deselectAllInFolder", "when": "view == xendit.explorerView && viewItem == directory", "group": "context@2"}]}, "configuration": {"title": "Xendit Copilot", "properties": {"xendit.githubToken": {"type": "string", "default": "", "description": "Token d'accès personnel (PAT) GitHub. Optionnel, mais recommandé pour augmenter les limites de taux de l'API: https://github.com/settings/personal-access-tokens", "scope": "machine-overridable"}, "xendit.maxRecentRepositories": {"type": "number", "default": 5, "description": "Nombre maximum de dépôts (autres que le dépôt 'Featured') à mémoriser dans la liste de sélection rapide.", "minimum": 1, "scope": "window"}, "xendit.maxConcurrentDownloads": {"type": "number", "default": 3, "description": "Nombre maximum de fichiers à télécharger simultanément.", "minimum": 1, "scope": "window"}, "xendit.showWelcomeOnStartup": {"type": "boolean", "default": true, "description": "Afficher la vue 'Get Started' au démarrage de VS Code.", "scope": "window"}, "xendit.autoRefreshInterval": {"type": ["number", "null"], "default": null, "description": "Intervalle (en secondes) pour rafraîchir automatiquement l'explorateur de dépôt. Mettre à 'null' pour désactiver.", "minimum": 10, "scope": "window"}, "xendit.includePaths": {"type": "string", "default": "", "description": "Liste de chemins ou noms de fichiers/dossiers à inclure dans l'explorateur, séparés par des virgules. Si vide, tout est inclus. Ex: .cursor,docs/,README.md", "scope": "window"}}}, "icons": {"xendit-logo": {"description": "Xendit Copilot", "default": {"fontPath": "assets/xendit.woff", "fontCharacter": "\\e900"}}}}, "scripts": {"vscode:package": "vsce package", "vscode:prepublish": "tsc -p ./", "vscode:publish": "vsce publish", "watch": "tsc -watch -p ./", "typecheck": "tsc --noEmit", "test": "npm run test:unit && npm run test:integration", "test:unit": "mocha --ui tdd out/test/unit/**/*.test.js", "test:integration": "node out/test/runTest.js", "test:coverage": "nyc npm run test", "pretest": "npm run vscode:prepublish && npm run compile-tests", "compile-tests": "node scripts/compile-tests.js", "package": "npm run vscode:prepublish && npm run vscode:package", "lint": "eslint src --ext ts", "lint:fix": "eslint src --ext ts --fix"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/mocha": "^10.0.10", "@types/nock": "^10.0.3", "@types/node": "20.x", "@types/sinon": "^17.0.4", "@types/vscode": "^1.96.0", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "^3.4.2", "eslint": "^9.23.0", "glob": "^10.4.5", "lefthook": "^1.11.10", "mocha": "^11.5.0", "nock": "^14.0.4", "nyc": "^17.1.0", "sinon": "^20.0.0", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.7.1+sha512.2d92c86b7928dc8284f53494fb4201f983da65f0fb4f0d40baafa5cf628fa31dae3e5968f12466f17df7e97310e30f343a648baea1b9b350685dafafffdf5808", "dependencies": {"@vscode/prompt-tsx": "^0.4.0-alpha.4"}}