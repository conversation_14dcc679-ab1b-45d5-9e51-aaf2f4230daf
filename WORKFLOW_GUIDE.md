# GitHub Actions Workflow Guide

This document explains how to use the improved GitHub Actions workflows for the Xendit Copilot VS Code extension.

## Overview

The project now includes two main workflows:

1. **CI Workflow** (`.github/workflows/ci.yml`) - Runs on pull requests and pushes
2. **Release Workflow** (`.github/workflows/release.yml`) - Creates releases with packaged extensions

## Workflows

### CI Workflow

**Triggers:**
- Pull requests to `main` branch
- Pushes to `main` branch (excluding documentation changes)
- Manual trigger via `workflow_dispatch`

**Jobs:**
1. **Lint and Type Check** - Validates code quality
2. **Test** - Runs unit tests and coverage
3. **Build and Package** - Compiles and packages the extension

### Release Workflow

**Triggers:**
- Pushes to `main` branch (excluding documentation changes)
- Manual trigger via `workflow_dispatch` with options:
  - Release type: patch/minor/major
  - Prerelease flag

**Jobs:**
1. **Validate** - Runs linting, type checking, and tests
2. **Package** - Creates and validates the VSIX package
3. **Release** - Creates GitHub release with the VSIX file

## Manual Workflow Execution

### Using GitHub CLI

You can trigger workflows manually using the GitHub CLI:

```bash
# Trigger CI workflow
gh workflow run ci.yml

# Trigger release workflow (basic)
gh workflow run release.yml

# Trigger release workflow with options
gh workflow run release.yml \
  --field release_type=minor \
  --field prerelease=true
```

### Using GitHub Web Interface

1. Go to your repository on GitHub
2. Click on the "Actions" tab
3. Select the workflow you want to run
4. Click "Run workflow"
5. Fill in any required inputs
6. Click "Run workflow"

## Package Management

The project uses **pnpm** as the primary package manager:

- `pnpm-lock.yaml` is the lock file
- `package.json` specifies `packageManager` field
- All workflows use pnpm for consistency

### Key Scripts

```bash
# Development
npm run watch              # Watch mode compilation
npm run typecheck         # Type checking only
npm run lint              # Lint code
npm run lint:fix          # Fix linting issues

# Testing
npm run test              # Run all tests
npm run test:unit         # Run unit tests only
npm run test:coverage     # Run tests with coverage

# Building
npm run vscode:prepublish # Compile TypeScript
npm run vscode:package    # Create VSIX package
npm run package           # Compile + package (combined)
```

## Workflow Features

### Security
- Validates GitHub tokens in code
- Uses modern GitHub Actions
- Proper secret handling

### Validation
- TypeScript compilation
- ESLint code quality checks
- Unit test execution
- VSIX package verification

### Artifacts
- VSIX packages are uploaded as artifacts
- 30-day retention for release artifacts
- 7-day retention for CI artifacts

### Release Notes
- Automatic generation based on commits
- Custom release notes with installation instructions
- Version extraction from package.json

## Troubleshooting

### Common Issues

1. **Package Manager Conflicts**
   - Ensure only `pnpm-lock.yaml` exists
   - Remove `package-lock.json` if present

2. **TypeScript Errors**
   - Run `npm run typecheck` locally
   - Fix type errors before pushing

3. **Test Failures**
   - Run `npm run test:unit` locally
   - Check test output for specific failures

4. **VSIX Package Issues**
   - Verify all required files are included
   - Check for security issues (tokens, secrets)

### Workflow Debugging

1. **Check workflow logs** in GitHub Actions tab
2. **Run commands locally** to reproduce issues
3. **Verify dependencies** are properly installed
4. **Check file permissions** and paths

## Best Practices

1. **Always run tests locally** before pushing
2. **Use semantic versioning** for releases
3. **Update version in package.json** before releases
4. **Review VSIX contents** before publishing
5. **Test extension installation** from VSIX files

## Version Management

The workflows automatically extract version information from `package.json`. To create a new release:

1. Update version in `package.json`
2. Commit and push to main branch
3. Workflow will automatically create a release with the new version

For manual releases with specific options, use the workflow dispatch feature.
