{
  "extends": "@istanbuljs/nyc-config-typescript",
  "include": [
    "out/services/**/*.js",
    "out/tools/**/*.js",
    "out/types/**/*.js"
  ],
  "exclude": [
    "out/test/**",
    "out/tools/toolsPrompt.js",
    "out/tools/toolParticipant.js",
  ],
  "reporter": [
    "text",
    "html",
    "lcov"
  ],
  "report-dir": "coverage",
  "check-coverage": true,
  "lines": 80,
  "statements": 80,
  "functions": 80,
  "branches": 70
}
