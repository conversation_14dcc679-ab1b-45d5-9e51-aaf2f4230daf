# File Preview Feature

The Xendit Copilot VS Code extension now includes a comprehensive file preview feature that allows users to quickly view selected files from GitHub repositories without editing them.

## Features

### 🔍 **Preview Icons**
- **Eye icon** (`$(eye)`) on each file line in the repository explorer
- Positioned to the **left of the check icon** for file selection
- Available for all supported file types
- **Inline preview**: Click the eye icon next to any file to preview it instantly

### 📄 **Preview Functionality**
- Opens selected files in **read-only VS Code editor tabs**
- Files open in **preview mode** with `preview: true` option
- Supports **multiple file selection** - opens each file in a separate tab
- **Intelligent file filtering** - only previews files, skips directories
- **Progress indicator** for multiple files

### 🎯 **Supported File Types**
The preview feature supports a wide range of file types:

#### **Code Files**
- JavaScript/TypeScript: `.js`, `.ts`, `.jsx`, `.tsx`
- Python: `.py`
- Java: `.java`
- C/C++: `.c`, `.cpp`
- C#: `.cs`
- PHP: `.php`
- Ruby: `.rb`
- Go: `.go`
- Rust: `.rs`
- Kotlin: `.kt`
- Swift: `.swift`

#### **Web Files**
- HTML: `.html`, `.htm`
- CSS: `.css`, `.scss`, `.sass`, `.less`

#### **Configuration Files**
- JSON: `.json`
- XML: `.xml`
- YAML: `.yaml`, `.yml`
- TOML: `.toml`
- INI: `.ini`, `.cfg`, `.conf`

#### **Documentation**
- Markdown: `.md`, `.markdown`, `.mdc`
- Text: `.txt`
- reStructuredText: `.rst`

#### **Scripts & Shell**
- Shell scripts: `.sh`, `.bash`, `.zsh`, `.fish`
- PowerShell: `.ps1`
- Batch: `.bat`, `.cmd`

#### **Special Files**
- `Dockerfile`
- `Makefile`
- `.gitignore`
- `.gitattributes`
- `.editorconfig`

### 🛡️ **Safety & Performance**
- **File size limit**: 1MB maximum per file
- **Preview limit**: Maximum 10 files at once
- **Read-only mode**: Files cannot be edited in preview
- **Error handling**: Graceful handling of missing files, network issues
- **Memory management**: Automatic cleanup of cached content

### 🎨 **User Experience**
- **Syntax highlighting**: Automatic language detection and highlighting
- **Loading states**: Progress indicators for multiple files
- **Error messages**: Clear, actionable error messages
- **Confirmation dialogs**: Warns when previewing many files

## Usage

### Basic Usage
1. **Browse files** in the repository explorer
2. **Click the eye icon** (👁️) next to any file you want to preview
3. **File opens** instantly in a read-only preview tab

### Bulk Preview
1. **Select multiple files** using checkboxes
2. **Use the "Preview Selected Files" command** from the command palette
3. **All selected files open** in separate preview tabs

### Multiple Files
- Select multiple files using checkboxes
- Click the preview button
- Each file opens in a separate tab
- Progress indicator shows loading status

### Error Handling
- **Unsupported files**: Shows warning message
- **Large files**: Shows size limit error
- **Network issues**: Shows connection error
- **Missing files**: Shows file not found error

## Technical Implementation

### Architecture
- **PreviewService**: Core service handling file preview logic
- **ExplorerView**: Integration with existing explorer view
- **Command Registration**: `xendit.previewSelected` command
- **Virtual Documents**: Uses VS Code's virtual document system

### Integration Points
- **SelectionService**: Gets currently selected files
- **GitHubApiService**: Fetches file content from GitHub
- **Logger**: Comprehensive logging for debugging
- **Error Handling**: Graceful error handling throughout

### File Content Provider
- Uses VS Code's `TextDocumentContentProvider`
- Virtual URI scheme: `xendit-preview:`
- Automatic content caching with memory management
- Language detection for proper syntax highlighting

## Configuration

The preview feature works with existing extension settings:
- **GitHub Token**: Required for accessing private repositories
- **Repository Selection**: Works with any connected GitHub repository
- **File Selection**: Uses existing checkbox selection system

## Limitations

- **Binary files**: Not supported (images, videos, archives)
- **Very large files**: Limited to 1MB for performance
- **Edit protection**: Files are read-only in preview mode
- **Network dependency**: Requires internet connection to fetch content

## Future Enhancements

Potential future improvements:
- **Diff view**: Compare file versions
- **Search in preview**: Find text within previewed files
- **Export options**: Save previewed content locally
- **Batch operations**: Preview entire directories
- **Custom viewers**: Support for more file types

## Troubleshooting

### Common Issues

**Preview button not visible**
- Ensure files are selected in the explorer
- Check that a repository is loaded

**Files not opening**
- Verify GitHub token is configured
- Check internet connection
- Ensure file type is supported

**Performance issues**
- Limit number of files previewed at once
- Check file sizes (1MB limit)
- Clear VS Code editor tabs if too many are open

### Debug Information
- Check the Xendit Copilot output log
- Use "Show Output" command for detailed logs
- File preview operations are logged with debug information

## API Reference

### PreviewService Methods

```typescript
// Preview multiple files
previewFiles(filePaths: string[], repository: GithubRepository): Promise<void>

// Get file content for preview
getFileContent(filePath: string, repository: GithubRepository): Promise<IPreviewFile>

// Check if file type is supported
isSupportedFileType(filePath: string): boolean
```

### Command
- **Command ID**: `xendit.previewSelected`
- **Title**: "Xendit Copilot: Preview Selected Files"
- **Icon**: `$(eye)`
- **When**: `view == xendit.explorerView`
