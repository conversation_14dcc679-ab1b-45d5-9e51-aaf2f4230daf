name: Release VS Code Extension

on:
  push:
    branches:
      - main
    paths-ignore:
      - '**.md'
      - '.gitignore'
      - 'LICENSE*'
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Release type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
      prerelease:
        description: 'Create as prerelease'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '20'

jobs:
  validate:
    name: Validate and Test
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.package_info.outputs.version }}
      name: ${{ steps.package_info.outputs.name }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: latest

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Extract package info
        id: package_info
        run: |
          VERSION=$(node -p "require('./package.json').version")
          NAME=$(node -p "require('./package.json').name")
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "name=$NAME" >> $GITHUB_OUTPUT
          echo "Extension: $NAME v$VERSION"

      - name: Lint code
        run: pnpm run lint

      - name: Type check
        run: pnpm run typecheck

      - name: Run tests
        run: pnpm run test:unit

      - name: Compile extension
        run: pnpm run vscode:prepublish

  package:
    name: Package Extension
    runs-on: ubuntu-latest
    needs: validate
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: latest

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Compile and package extension
        run: pnpm run package

      - name: Verify VSIX file
        id: verify_vsix
        run: |
          vsix_file=$(find . -name "*.vsix" -print -quit)
          if [ -z "$vsix_file" ]; then
            echo "Error: No VSIX file found"
            exit 1
          fi

          echo "vsix_path=$vsix_file" >> $GITHUB_OUTPUT
          echo "vsix_name=$(basename "$vsix_file")" >> $GITHUB_OUTPUT

          # Verify the VSIX file is valid
          npx vsce ls "$vsix_file"

          echo "✅ VSIX file created successfully: $vsix_file"

      - name: Upload VSIX artifact
        uses: actions/upload-artifact@v4
        with:
          name: vsix-package
          path: ${{ steps.verify_vsix.outputs.vsix_path }}
          retention-days: 30

  release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: [validate, package]
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Download VSIX artifact
        uses: actions/download-artifact@v4
        with:
          name: vsix-package

      - name: Get VSIX file info
        id: vsix_info
        run: |
          vsix_file=$(find . -name "*.vsix" -print -quit)
          echo "vsix_path=$vsix_file" >> $GITHUB_OUTPUT
          echo "vsix_name=$(basename "$vsix_file")" >> $GITHUB_OUTPUT

      - name: Generate release notes
        id: release_notes
        run: |
          cat > release_notes.md << 'EOF'
          ## 🚀 Release ${{ needs.validate.outputs.name }} v${{ needs.validate.outputs.version }}

          ### What's Changed
          - Extension package built and tested successfully
          - All validation checks passed

          ### Installation
          Download the `.vsix` file and install it in VS Code:
          ```bash
          code --install-extension ${{ steps.vsix_info.outputs.vsix_name }}
          ```

          Or install via VS Code UI: Extensions → Install from VSIX...
          EOF

          echo "notes_file=release_notes.md" >> $GITHUB_OUTPUT

      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: v${{ needs.validate.outputs.version }}
          name: Release v${{ needs.validate.outputs.version }}
          body_path: ${{ steps.release_notes.outputs.notes_file }}
          files: ${{ steps.vsix_info.outputs.vsix_path }}
          draft: false
          prerelease: ${{ github.event.inputs.prerelease == 'true' }}
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
