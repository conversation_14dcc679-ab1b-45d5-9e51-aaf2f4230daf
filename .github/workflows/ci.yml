name: CI

on:
  pull_request:
    branches: [main]
  push:
    branches: [main]
    paths-ignore:
      - '**.md'
      - '.gitignore'
      - 'LICENSE*'
  workflow_dispatch:

env:
  NODE_VERSION: '20'

jobs:
  lint-and-typecheck:
    name: Lint and Type Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: latest

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Lint code
        run: pnpm run lint

      - name: Type check
        run: pnpm run typecheck

  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: latest

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run unit tests
        run: pnpm run test:unit

      - name: Run test coverage
        run: pnpm run test:coverage

  build-and-package:
    name: Build and Package
    runs-on: ubuntu-latest
    needs: [lint-and-typecheck, test]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: latest

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build extension
        run: pnpm run vscode:prepublish

      - name: Package extension
        run: pnpm run vscode:package

      - name: Verify package
        run: |
          vsix_file=$(find . -name "*.vsix" -print -quit)
          if [ -z "$vsix_file" ]; then
            echo "❌ Error: No VSIX file found"
            exit 1
          fi
          
          echo "✅ VSIX file created: $vsix_file"
          
          # List contents of the VSIX file
          npx vsce ls "$vsix_file"

      - name: Upload package artifact
        uses: actions/upload-artifact@v4
        with:
          name: extension-package
          path: "*.vsix"
          retention-days: 7
